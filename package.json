{"name": "histreams-rn", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@tamagui/babel-plugin": "^1.72.1", "@tamagui/config": "^1.72.1", "@tamagui/lucide-icons": "^1.76.0", "@tamagui/toast": "^1.75.4", "axios": "^1.5.1", "babel-plugin-transform-inline-environment-variables": "^0.4.4", "bitmovin-player-react-native": "0.37.0", "burnt": "^0.12.1", "dayjs": "^1.11.10", "expo": "^52.0.37", "expo-application": "~6.0.2", "expo-blur": "~14.0.3", "expo-build-properties": "~0.13.2", "expo-constants": "~17.0.7", "expo-keep-awake": "~14.0.3", "expo-linking": "~7.0.5", "expo-router": "~4.0.17", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.1", "react": "18.3.1", "react-dom": "^18.0.0", "react-native": "0.76.7", "react-native-base64": "^0.2.1", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "tamagui": "^1.125.19"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.3.12", "@types/react-native-base64": "^0.2.1", "typescript": "^5.1.3"}, "private": true}