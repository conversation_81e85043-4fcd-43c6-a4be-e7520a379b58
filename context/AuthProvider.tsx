import { useSegments, useRouter } from "expo-router";
import { createContext, useContext, useEffect, useState } from "react";
import AsyncStorage from '@react-native-async-storage/async-storage';

type User = {
  access_token: string;
  refresh_token: string;
}

type AuthType = {
  user: User | null;
  setUser: (user: User | null) => void;
}

const AuthContext = createContext<AuthType>({
  user: null,
  setUser: () => {},
});



export const useAuth = () => useContext(AuthContext);

function useProtectedRoute(user: any, setUser: React.Dispatch<React.SetStateAction<User | null>>) {
  const segments = useSegments();
  const router = useRouter();

  const getData = async () => {
    try {
      const jsonValue = await AsyncStorage.getItem('user_token');
      console.log('AuthProvider - useEffect - getData')
      console.log(jsonValue)
      if (jsonValue) {
        setUser(jsonValue as any)
      } else {
        setUser(null)
      }
      return jsonValue;
    } catch (e) {
      // error reading value
    }
  };

  useEffect(() => {
    const inAuthGroup = segments[0] === "(auth)";
    const json = getData()
    if (
      // If the user is not signed in and the initial segment is not anything in the auth group.
      !user &&
      !inAuthGroup
    ) {
      // Redirect to the sign-in page.
      const json = getData()
      router.replace("/login");
    } else if (user && inAuthGroup) {
      // Redirect away from the sign-in page.
      router.replace("/home");
    }
  }, [user, segments]);
}

export function AuthProvider({ children }: { children: JSX.Element }): JSX.Element {
    const [user, setUser] = useState<User | null>(null);

    useProtectedRoute(user, setUser);

    const authContext: AuthType = {
      user,
      setUser,
    };

  return <AuthContext.Provider value={authContext} >{children}</AuthContext.Provider>;
}