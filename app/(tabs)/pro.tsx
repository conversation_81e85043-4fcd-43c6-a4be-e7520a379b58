import { View, Image, Platform, ScrollView } from "react-native";
import {
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { Paragraph, SizableText, Text, XStack, YStack, Card, useTheme } from 'tamagui'
import {Stack, router } from 'expo-router';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';
import { useState } from 'react'
import { useFocusEffect } from 'expo-router';

export default function Pro() {
  const theme = useTheme()
  const insets = useSafeAreaInsets();
  const background = theme.background.get()
  const [switch18, setSwitch18] = useState(false);

  function pushToF1archive() {
    router.push({pathname:'/pages/f1archive',})
  }

  function pushToViaplay() {
    router.push({pathname:'/pages/viaplay',})
  }

  function pushToTheav() {
    router.push({pathname:'/pages/theav',})
  }


  function pushToBeesport() {
    router.push({pathname:'/pages/beesport',})
  }

  useFocusEffect(() => {
    getData();
  });

  const getData = async () => {
    try {
      const s = await AsyncStorage.getItem('18+');
      console.log(s);
      
      if (s !== null) {
        setSwitch18(s=='1'?true:false);
      }
    } catch (e) {
      // error reading value
    }
  };


  const showAlert = (msg:any) =>
    Alert.alert(
      '',
        msg,
      [
        {
          text: 'close',
          onPress: () =>{  {}},
          style: 'destructive',
        },
      ],
      {
        cancelable: true
      },
    );

  async function pushToHami() {
    const jsonValue = await AsyncStorage.getItem('user_token');
    const json = jsonValue != null ? JSON.parse(jsonValue) : null;
    axios({
      method: 'get',
      url: '/api/info',
      headers: {
        Authorization: 'Bearer ' + json.access_token,
      },
    }).then((response) => {
      const userInfo = response.data;
      let sub;
      userInfo.sub.forEach(function (item:any) {
        if (item.type == 2 && (item.subscription_time + 3600 * 24 * item.subscription_countdown) * 1000 > Date.now() ) {
          sub = item;
        }
      });

      if (sub) {
        // showAlert('愛爾達订阅已过期')
        router.push({pathname:'/pages/hami'})
      } else {
        showAlert('愛爾達订阅已过期')
      }
      
    })
    
  }

  function pushToStarplus() {
    router.push({pathname:'/pages/starplus',})
  }

  function pushToStarplusMore() {
    router.push({pathname:'/pages/starplusmore',})
  }

  function pushToEurosport() {
    router.push({pathname:'/pages/eurosport',})
  }

  function pushToViaplay4K() {
    router.push({pathname:'/pages/player', params:{id: 0, type: 12}})
  }

  function pushToMv() {
    router.push({pathname:'/pages/mv',})
  }

  return (
    <ScrollView style={{ flex: 1, backgroundColor: background }} 
                contentContainerStyle={{ paddingTop: insets.top, paddingBottom: 20 }}>
      <Stack.Screen
        options={{
          title: '',
          headerTintColor: '#fff',
          headerStyle: { backgroundColor: '#242c40'},
          headerShown: false
        }}
      />
      <XStack $sm={{ flexDirection: 'row',  }} paddingHorizontal="$4" space>
        <Card elevate size="$8" width={'47%'} onPress={() =>  pushToF1archive()} >
          <Card.Header padded>
            <Paragraph theme="alt2"></Paragraph>
          </Card.Header>
          <Card.Footer padded>
            <XStack flex={1} />
          </Card.Footer>
          <Card.Background style={{justifyContent: 'center', alignItems:"center"}}>
            <Image
              resizeMode="contain"
              style={{width: '100%',height:100,  justifyContent: 'center'}}
              source={require('../../assets/f1_logo.png')}
            />
          </Card.Background>
        </Card>
        <Card elevate size="$8" width={'47%'} onPress={() =>  pushToHami()} >
          <Card.Header padded>
            <Paragraph theme="alt2"></Paragraph>
          </Card.Header>
          <Card.Footer padded>
            <XStack flex={1} />
          </Card.Footer>
          <Card.Background style={{justifyContent: 'center', alignItems:"center"}}>
            <Image
              resizeMode="contain"
              style={{width: '80%',height:100,}}
              source={require('../../assets/hami.png')}
            />
          </Card.Background>
        </Card>
        
      </XStack>
      {/* {<XStack  $sm={{ flexDirection: 'row',  }} style={{marginTop: 20}} paddingHorizontal="$4" space>
        <Card elevate size="$8" width={'47%'}  style={{backgroundColor:'#271F44'}} onPress={() =>  pushToStarplus()} >
          <Card.Header padded>
            <Paragraph theme="alt2"></Paragraph>
          </Card.Header>
          <Card.Footer style={{ paddingBottom: '26%' }}>
            <Text style={{textAlign:'center', width:'100%'}}>Sports</Text>
          </Card.Footer>
          <Card.Background style={{justifyContent: 'center', alignItems:"center"}}>
            <Image
              resizeMode="contain"
              style={{width: '100%',height:130,}}
              source={require('../../assets/star+logo.png')}
            />
          </Card.Background>
        </Card>
        <Card elevate size="$8" width={'47%'}  style={{backgroundColor:'#271F44'}} onPress={() =>  pushToStarplusMore()} >
          <Card.Header padded>
          </Card.Header>
          <Card.Footer style={{ paddingBottom: '26%' }}>
            <Text style={{textAlign:'center', width:'100%'}}>Movies & Series</Text>
          </Card.Footer>
          <Card.Background style={{justifyContent: 'center', alignItems:"center"}}>
            <Image
              resizeMode="contain"
              style={{width: '100%',height:130,}}
              source={require('../../assets/star+logo.png')}
            />
          </Card.Background>
        </Card>
      </XStack> } */}
      <XStack  $sm={{ flexDirection: 'row',  }} style={{marginTop: 20}} paddingHorizontal="$4" space>
        
        <Card elevate size="$8" width={'47%'} onPress={() =>  pushToBeesport()} >
          <Card.Header padded>
            <Paragraph theme="alt2"></Paragraph>
          </Card.Header>
          <Card.Footer padded>
            <XStack flex={1} />
          </Card.Footer>
          <Card.Background style={{justifyContent: 'center', alignItems:"center"}}>
          <Text style={{textAlign:'center', width:'100%'}}>Sports</Text>
          </Card.Background>
        </Card>
        {switch18 && <Card elevate size="$8" width={'47%'} onPress={() =>  pushToTheav()} >
          <Card.Header padded>
            <Paragraph theme="alt2"></Paragraph>
          </Card.Header>
          <Card.Footer padded>
            <XStack flex={1} />
          </Card.Footer>
          <Card.Background style={{justifyContent: 'center', alignItems:"center"}}>
            <Text fontSize={50} style={{ display: 'flex', justifyContent: 'center', alignItems: 'center'}}>🔞</Text>
          </Card.Background>
        </Card>}
        {/* { Platform.OS === 'android' && <Card elevate size="$8" width={'47%'}  onPress={() => pushToEurosport()}>
          <Card.Header padded>
            <Paragraph theme="alt2"></Paragraph>
          </Card.Header>
          <Card.Footer padded>
            <XStack flex={1} />
          </Card.Footer>
           <Card.Background style={{justifyContent: 'center', alignItems:"center"}}>
              <Image
                resizeMode="contain"
                style={{width: '80%',height:100, }}
                source={require('../../assets/Eurosport_Logo_0.png')}
              />
          </Card.Background>
          </Card>} */}
        
      </XStack> 
      {/* <XStack  $sm={{ flexDirection: 'row',  }} style={{marginTop: 20}} paddingHorizontal="$4" space>
        <Card elevate size="$8" width={'47%'} onPress={() =>  pushToTheav()} >
          <Card.Header padded>
            <Paragraph theme="alt2"></Paragraph>
          </Card.Header>
          <Card.Footer padded>
            <XStack flex={1} />
          </Card.Footer>
          <Card.Background style={{justifyContent: 'center', alignItems:"center"}}>
            <Image
              resizeMode="contain"
              style={{width: '80%',height:130,}}
              source={require('../../assets/viaplay_white.png')}
            />
          </Card.Background>
        </Card>
      </XStack>  */}
      
      {/* <XStack  $sm={{ flexDirection: 'row',  }} style={{marginTop: 20}} paddingHorizontal="$4" space>
      { Platform.OS === 'android' && <Card elevate size="$8" width={'47%'} onPress={() =>  pushToViaplay()} >
          <Card.Header padded>
            <Paragraph theme="alt2"></Paragraph>
          </Card.Header>
          <Card.Footer padded>
            <XStack flex={1} />
          </Card.Footer>
          <Card.Background style={{justifyContent: 'center', alignItems:"center"}}>
            <Image
              resizeMode="contain"
              style={{width: '80%',height:130,}}
              source={require('../../assets/viaplay_white.png')}
            />
          </Card.Background>
        </Card>}
      </XStack>  */}
      
      {/* <XStack $sm={{ flexDirection: 'row',  }} style={{marginTop: 20}} paddingHorizontal="$4" space>
        <Card elevate size="$8" width={'47%'} onPress={() => pushToMv()} >
          <Card.Header padded>
            <Paragraph theme="alt2"></Paragraph>
          </Card.Header>
          <Card.Footer padded>
            <XStack flex={1} />
          </Card.Footer>
          <Card.Background style={{justifyContent: 'center', alignItems:"center"}}>
            <Image
              resizeMode="contain"
              style={{width: '80%',height:100,}}
              source={require('../../assets/mv.png')}
            />
          </Card.Background>
        </Card>
      </XStack> */}
      
    </ScrollView>
  );
}
