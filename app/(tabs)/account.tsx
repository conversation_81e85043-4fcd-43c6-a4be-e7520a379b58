import { View, StyleSheet, } from "react-native";
import { useAuth } from "../../context/AuthProvider";
import { TouchableOpacity } from "react-native";
import { useTheme, Text, Card, Switch} from 'tamagui';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {Stack } from 'expo-router';
import Ionicons from '@expo/vector-icons/Ionicons';
import { useEffect, useState } from 'react'
import {useSafeAreaInsets,} from 'react-native-safe-area-context';
import * as Application from 'expo-application';
import { Input } from 'tamagui';
import { useToastController } from '@tamagui/toast'
import axios from 'axios';

export default function Accout() {
  const toast = useToastController();
  const insets = useSafeAreaInsets();
  const theme = useTheme()
  const background = theme.background.get()
  const { setUser, user } = useAuth();
  const [email, setEmail] = useState('');

  const [api, setApi] = useState('');
  const [switch18, setSwitch18] = useState(false);
  const [switch4k, setSwitch4k] = useState(false);

  const removeValue = async () => {
    try {
      await AsyncStorage.removeItem('user_token')
      console.log('setUser Done.')
      setUser(null)
    } catch(e) {
      // remove error
    }
  
    console.log('Done.')
  }

  useEffect(() => {
    getData()
  },[]);

  const getData = async () => {
    try {
      const e = await AsyncStorage.getItem('email');
      const a = await AsyncStorage.getItem('api');
      const s = await AsyncStorage.getItem('18+');
      const k = await AsyncStorage.getItem('4k');
      if (e !== null) {
        setEmail(e)
      }

      if (a !== null) {
        setApi(a)
      }
      console.log(s);
      if (s !== null) {
        setSwitch18(s=='1'?true:false);
      }
      if (k !== null) {
        setSwitch4k(k=='1'?true:false);
      }
    } catch (e) {
      // error reading value
    }
  };

  function setApiOnBlur() {
    AsyncStorage.setItem('api', api).then( e => {
      axios.defaults.baseURL = api
      toast.show('Successfully saved!', {
        native: true,
      })
    })
  }

  function onCheckedChange(checked:boolean) {
    setSwitch18(checked);
    AsyncStorage.setItem('18+', checked? '1': '0').then( e => {
      toast.show('Successfully saved!', {
        native: true,
      })
    })
  }

  function onChecked4kChange(checked:boolean) {
    setSwitch4k(checked);
    AsyncStorage.setItem('4k', checked? '1': '0').then( e => {
      toast.show('Successfully saved!', {
        native: true,
      })
    })
  }

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingTop: 22,
    },
    item: {
      padding: 5,
      fontSize: 18,
    },
  });
  
  return (
    <View style={{ flex: 1, paddingTop: insets.top, alignItems: "center",  backgroundColor: background}}>
      <Stack.Screen
        options={{
          title: '',
          headerTintColor: '#fff',
          headerStyle: { backgroundColor: '#242c40'},
          headerShown: false
        }}
      />
      <Card  style={{padding: 10}} width={'98%'}>
        <View style={{display:"flex", flexDirection:"row", alignItems:"center", justifyContent:"space-between"}}>
          <View>
            <Text style={styles.item} fontFamily="$body" color={'white'} >Email</Text>
          </View>
          <View style={{display:"flex", flexDirection:"row", alignItems:"center",}}>
            <View style={{alignItems:"flex-end"}}>
              <Text  fontFamily="$body" style={styles.item}  color={ 'white'} >{email}</Text>
            </View>
          </View>
        </View>
      </Card>
      <Card  style={{padding: 10, marginTop:10}} width={'98%'} >
        <View style={{display:"flex", flexDirection:"row", alignItems:"center", justifyContent:"space-between"}}>
          <View>
            <Text style={styles.item} fontFamily="$body" color={'white'} >Version</Text>
          </View>
          <View style={{display:"flex", flexDirection:"row", alignItems:"center",}}>
            <View style={{alignItems:"flex-end"}}>
              <Text  fontFamily="$body" style={styles.item}  color={ 'white'} >{ `${Application.nativeApplicationVersion}(${Application.nativeBuildVersion})` }</Text>
            </View>
          </View>
        </View>
      </Card>
      <Card  style={{padding: 10, marginTop:10}} width={'98%'} >
        <View style={{display:"flex", flexDirection:"row", alignItems:"center", justifyContent:"space-between"}}>
          <View>
            <Text style={styles.item} fontFamily="$body" color={'white'} >API</Text>
          </View>
          <View style={{display:"flex", flexDirection:"row", alignItems:"center",}}>
            <View style={{alignItems:"flex-end"}}>
              {/* <Text  fontFamily="$body" style={styles.item}  color={ 'white'} >{ api }</Text> */}
              <Input value={api} onBlur={ setApiOnBlur } onChangeText={api => setApi(api)} size="$4" borderWidth={2} placeholder="api"/>
            </View>
          </View>
        </View>
      </Card>
      <Card  style={{padding: 10, marginTop:10}} width={'98%'} >
        <View style={{display:"flex", flexDirection:"row", alignItems:"center", justifyContent:"space-between"}}>
          <View>
            <Text style={styles.item} fontFamily="$body" color={'white'} >4K</Text>
          </View>
          <View style={{display:"flex", flexDirection:"row", alignItems:"center",}}>
            <View style={{alignItems:"flex-end"}}>
              <Switch size="$4" checked={switch4k} onCheckedChange={checked => onChecked4kChange(checked)}><Switch.Thumb animation="bouncy" /></Switch>
            </View>
          </View>
        </View>
      </Card>
      <Card  style={{padding: 10, marginTop:10}} width={'98%'} >
        <View style={{display:"flex", flexDirection:"row", alignItems:"center", justifyContent:"space-between"}}>
          <View>
            <Text style={styles.item} fontFamily="$body" color={'white'} >18+</Text>
          </View>
          <View style={{display:"flex", flexDirection:"row", alignItems:"center",}}>
            <View style={{alignItems:"flex-end"}}>
              <Switch size="$4" checked={switch18} onCheckedChange={checked => onCheckedChange(checked)}><Switch.Thumb animation="bouncy" /></Switch>
            </View>
          </View>
        </View>
      </Card>
      <Card  style={{padding: 10, marginTop:10}} width={'98%'} height={50}>
        <View style={{display:"flex", justifyContent:"center", alignItems:'center'}}>
          <TouchableOpacity onPress={() => removeValue()}>
              <Text fontFamily="$body" fontSize={20}>Log out</Text>
          </TouchableOpacity>
        </View>
      </Card>
      
    </View>
  );
}
