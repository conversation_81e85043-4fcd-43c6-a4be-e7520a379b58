import { View,StyleSheet, FlatList, Dimensions, RefreshControl } from "react-native";
import { <PERSON>, useRouter, Stack } from 'expo-router';
import React, { useCallback, useRef, useEffect,  useState } from 'react';
import { Paragraph, SizableText, Text, XStack, YStack, Image, Card, useTheme } from 'tamagui'
import {useSafeAreaInsets,} from 'react-native-safe-area-context';
import Ionicons from '@expo/vector-icons/Ionicons';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from 'expo-router';

export default function Home() {
  const router = useRouter();
  const theme = useTheme()
  const insets = useSafeAreaInsets();
  const background = theme.background.get()

  const [listData, setData] = useState<Array<Event>>([]);
  const [headerString, setHeaderString] = useState('');
  const [showsData, setShowsData] = useState<Array<showSession>>([]);
  const [showsListData, setShowsListData] = useState<Array<show>>([]);

  
  const [selectedId, setSelectedId] = useState<string>();

  const [loading, setLoading] = useState(true);
  const [nextEvent, setNextEvent] = useState<Event>();
  const [countdown, setCountdown] = useState(0);

  const [weekendLoading, setWeekendLoading] = useState(true);
  const [weekendListData, setWeekendData] = useState([]);
  
  

  type Event = {
    id?: string;
    title?: string;
    Global_Meeting_Name?: string;
    shortDescription?: string;
    duration?: number;
    sessionStartDate?: number;
    sessionEndDate?: number;
    contentSubtype?: string;
    contentType?: string;
    Series?: string;
    pictureUrl?: string;
  };

  type showSession = {
    title?: string;
    pictureUrl?: string;
    pid?: string;
  };

  type weekendEvent = {
    id?: string;
    title?: string;
    pictureUrl?: string;
    pid?: string;
    series?: string;
  };



  type show = {
    title?: string;
    retrieveItems?: Array<showEvent>;
  };

  type showEvent = {
    id?: string;
    title?: string;
    pictureUrl?: string;
  };

  const [hasRun, setHasRun] = useState(false);

  useEffect(() => {
    console.log('useEffect');
    
    setTimeout(() => {
      onRefresh();
    }, 2);
  }, []);
  
  function onRefresh() {
    renderCard();
    renderShowsCard();
  }

  function renderCard() {
    setLoading(true);
    axios({
      method: 'get',
      url: '/api/schedule',
    })
      .then((response) => {
        const array: Array<Record<string, unknown>> = response.data.containers;
        let schedule;
        array.map((item) => {
          if (item.layout == 'schedule') {
            schedule = item;
          }
        });
        if (schedule) {
          const carray: Array<Record<string, unknown>> =
            schedule['retrieveItems']['resultObj']['containers'];
          const ea: Array<Event> = [];
          carray.map((item) => {
            if (item['eventName'] == 'ALL') {
              const dataSource: Array<Event> = [];
              const earray: Array<any> = item['events'] as Array<any> ;
              if (earray.length > 0) {
                earray.map((eitem) => {
                  const e: Event = {};
                  e.id = eitem['id'].toString();
                  e.title = eitem['metadata']['titleBrief'];
                  e.shortDescription = eitem['metadata']['shortDescription'];
                  e.sessionStartDate =
                  eitem['metadata']['emfAttributes']['sessionStartDate'];
                  e.sessionEndDate =
                  eitem['metadata']['emfAttributes']['sessionEndDate'];
                  e.contentSubtype = eitem['metadata']['contentSubtype'];
                  e.contentType = eitem['metadata']['contentType'];
                  e.Series = eitem['metadata']['emfAttributes']['Series'];
                  e.duration = eitem['metadata']['duration'];
                  e.Global_Meeting_Name =
                  eitem['metadata']['emfAttributes']['Global_Meeting_Name'];
                  e.pictureUrl = eitem['metadata']['pictureUrl'];
                  dataSource.push(e);
                  if (headerString.length == 0) {
                    setHeaderString(e.Global_Meeting_Name!);
                  }
                  // }
                });
                const ascDataSource = dataSource.sort(
                  (a, b) => a.sessionStartDate! - b.sessionStartDate!
                );
                setData(ascDataSource);
              }
            }
            
          });
          
        } else {
          setData([]);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }

  function renderShowsCard() {
    setLoading(true);
    const dataSource: Array<showSession> = [];
    axios({
      method: 'get',
      url: '/api/shows',
    })
      .then((response) => {
        const array: Array<Record<string, any>> = response.data;
        array.map((show) => {
          const a: showSession = {};
          a.pictureUrl = show["imageUrl"].toString();
          a.title = show["title"].toString();
          a.pid = show["pid"].toString();
          dataSource.push(a);
        });
        setShowsData(dataSource);
      })
      .finally(() => {
        setLoading(false);
      });
  }

  async function renderShowsDetail(id:any) {
    router.push({pathname:'/pages/shows', params:{id: id}})
  }

  function pushToPlayer(id:any) {
    router.push({pathname:'/pages/player', params:{id: id}})
  }


  function formatCell(item: Event) {
    const nowDate = new Date();
    const startDate = new Date(item.sessionStartDate!);
    // newDate.setTime(start);

    const endDate = new Date(item.sessionEndDate!);
    // endDate.setTime(end);

    const day = startDate.getDay();
    const date = startDate.getDate();
    const month = startDate.getMonth();
    const hour = startDate.getHours();
    let minute = startDate.getMinutes().toString();
    minute = Number(minute) > 9 ? minute : '0' + minute;
    const endhour = endDate.getHours();

    let endminute = endDate.getMinutes().toString();
    endminute = Number(endminute) > 9 ? endminute : '0' + endminute;
    // return date + ' ' + hour + ':' + minute + '-' + endhour + ':' + endminute;
    const dateString = `${month+1}-${date}`;
    const timeString = hour + ':' + minute + '-' + endhour + ':' + endminute;
    const weekString = ['Sun', 'Mon', 'Tues', 'Wed', 'Thur', 'Fri', 'Sat'][day];
    const isStart = (startDate.valueOf() - 1000 * 60 * 5) < nowDate.valueOf()

    return (

      <Card key={item.id!} style={{padding: 10}} onPress={() => isStart ? pushToPlayer(item.id!) : {}}>
        <View style={{display:"flex", flexDirection:"row", alignItems:"center", justifyContent:"space-between"}}>
          <View>
            <Text style={styles.item} fontFamily="$body" color={ isStart ? 'white' : 'gray'} >{dateString}</Text>
            <Text style={styles.item} fontFamily="$body" color={ isStart ? 'white' : 'gray'} >{weekString}</Text>
          </View>
          <View style={{display:"flex", flexDirection:"row", alignItems:"center",}}>
            <View style={{alignItems:"flex-end"}}>
              <Text  fontFamily="$body" style={styles.item}  color={ isStart ? 'white' : 'gray'} >{item.title}</Text>
              <View style={{display:"flex", flexDirection:"row"}} >
                <Text  fontFamily="$body" style={styles.item} color={ isStart ? 'white' : 'gray'} >{item.Series}</Text>
                <Text  fontFamily="$body" style={styles.item} color={ isStart ? 'white' : 'gray'} >{timeString}</Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={32} color={ isStart ? 'white' : 'gray'} />
          </View>
        </View>
      </Card>
    );
  }

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingTop: 22,
    },
    item: {
      padding: 5,
      fontSize: 18,
    },
  });

  const setToken = async () => {
    const gjson = await AsyncStorage.getItem('user_token');
    const json = gjson != null ? JSON.parse(gjson) : null;
    json.access_token = '3333'

    const jsonValue = JSON.stringify(json);
    AsyncStorage.setItem('user_token', jsonValue);
  }
  
  
  return (
    <View style={{ paddingTop: insets.top, backgroundColor: background}}>
      <Stack.Screen
        
        options={{
          title: '',
          headerTintColor: '#fff',
          headerStyle: { backgroundColor: '#202020',  },
        }}
      />
      <SizableText size="$9" fontFamily="$body" style={{padding: 10}}>{headerString}</SizableText>
      <View 
        style={{height: Dimensions.get('window').height - 360}}>
          {/* <Button onPress={ setToken }>ssss</Button> */}
        <FlatList
          refreshControl={
            <RefreshControl refreshing={loading} onRefresh={onRefresh}  tintColor="#fff"
            titleColor="#fff"/>
          }
          nestedScrollEnabled
          data={listData}
          keyExtractor={item => item.id!}
          extraData={selectedId}
          renderItem={({item}) => 
          <View key={item.id!} style={ {marginBottom: 10, marginLeft: 10, marginRight: 10}} >
            {formatCell(item)}
          </View>}
        />
      </View>
      <View style={{height:260}}>
        <SizableText size="$9" fontFamily="$body" style={{padding: 10}}>Shows</SizableText>
        {/* <Link href={{ pathname: '/pages/player',  }}>Go to player</Link> */}
        <FlatList
          data={showsData}
          horizontal={true}
          nestedScrollEnabled
          renderItem={({item}) => 
          <View key={item.pid!}  style={ {marginBottom: 10, marginLeft: 10, marginRight: 10,}}>
            <Card key={item.pid!} style={{width: 200,
                    height: 140, overflow: "hidden"}} onPress={() =>  renderShowsDetail(item.pid!)}>
              <Card.Header padded></Card.Header>
              <Card.Footer >
                <Text style={ {margin:4,}}>{item.title!}</Text>
              </Card.Footer>
              <Card.Background>
                <Image
                  resizeMode="contain"
                  alignSelf="center"
                  source={{
                    width: 200,
                    height: 110,
                    uri: item.pictureUrl!
                  }}
                />
              </Card.Background>
            </Card>
          </View>}
        />
      </View>
    </View>
  );
}

