import { FontAwesome } from "@expo/vector-icons";
import { BottomTabBar } from "@react-navigation/bottom-tabs";
import { BlurView } from "expo-blur";
import { Tabs } from "expo-router";
import React from "react";
import { Platform, View, Text } from "react-native";

export default function TabsLayout() {
  return (
    <Tabs
      initialRouteName="home"
      screenOptions={{
        tabBarStyle:
           {
                backgroundColor: "transparent",
                borderTopWidth: 0,
                elevation: 0
              },
        headerShown: false,
      }}
      tabBar={(props) =>
        // Platform.OS === "ios" ? (
        //   <BlurView
        //     style={{ position: "absolute", bottom: 0, left: 0, right: 0 }}
        //     intensity={95}
        //   >
        //     <BottomTabBar {...props} />
        //   </BlurView>
        // ) : 
        (
          <BottomTabBar {...props} />
        )
      }
    >
      <Tabs.Screen
        name="home"
        options={{
          href: "/home",
          title: "",
          tabBarIcon: ({ focused, color }) => (
            <View
              style={{
                flexDirection: "column",
                alignItems: "center",
                marginTop: 0,
                backgroundColor: "transparent",
              }}
            >
              <TabBarIcon name="calendar" color={focused? "white": "gray"} size={28} />
              {/* <Text style={{ marginTop: 5, fontSize: 10, opacity: 0.5, color:focused? "black": "white" }}>
                Home
              </Text> */}
            </View>
          ),
        }}
      />
      <Tabs.Screen
        name="pro"
        options={{
          title: "",
          headerShown: false,
          href: {
            pathname: "/pro",
          },
          
          tabBarIcon: ({ focused, color }) => (
            <View
              style={{
                flexDirection: "column",
                alignItems: "center",
                marginTop: 5,
                backgroundColor: "transparent",
              }}
            >
              <TabBarIcon name="navicon" color={focused? "white": "gray"} size={30} />
              {/* <Text style={{ marginTop: 5, fontSize: 10, opacity: 0.5, color: focused? "black": "white" }}>
                Pro
              </Text> */}
            </View>
          ),
        }}
      />
      <Tabs.Screen
        name="account"
        options={{
          title: "",
          headerShown: true,
          href: {
            pathname: "/account",
          },
          
          tabBarIcon: ({ focused, color }) => (
            <View
              style={{
                flexDirection: "column",
                alignItems: "center",
                marginTop: 0,
                backgroundColor: "transparent",
              }}
            >
              <TabBarIcon name="user" color={focused? "white": "gray"} size={30} />
              {/* <Text style={{ marginTop: 5, fontSize: 10, opacity: 0.5, color: "white" }}>
                Account
              </Text> */}
            </View>
          ),
        }}
      />
      
    </Tabs>
  );
}

function TabBarIcon(props: {
  name: React.ComponentProps<typeof FontAwesome>["name"];
  color: string;
  size?: number;
}) {
  return (
    <FontAwesome
      size={props.size || 26}
      style={{ marginBottom: -3 }}
      {...props}
    />
  );
}
