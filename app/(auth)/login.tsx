import { View, Image} from "react-native";
import { useAuth } from "../../context/AuthProvider";
import { Input } from 'tamagui';
import { useEffect, useState } from 'react'
import { <PERSON><PERSON>, Form, H4, <PERSON><PERSON>, <PERSON>Stack,} from 'tamagui';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {  Stack } from 'expo-router';

export default function Login() {
  const { setUser } = useAuth();
  const [status, setStatus] = useState<'off' | 'submitting' | 'submitted'>('off');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const login = () => {
    getLogin()
  }

  useEffect(() => {
    getData()
  }, []);

  const getData = async () => {
    try {
      const e = await AsyncStorage.getItem('email');
      if (e !== null) {
        setEmail(e)
        // value previously stored
      }
      const p = await AsyncStorage.getItem('password');
      if (p !== null) {
        setPassword(p)
        // value previously stored
      }
    } catch (e) {
      // error reading value
    }
  };

  const getLogin = async () => {
    setStatus('submitting');
    const j = JSON.stringify({
      'email': email,
      'password': password,
    })
    console.log(j)
    axios
      .post('/api/login', j)
      .then((res) => {
        const jsonValue = JSON.stringify(res.data);
        AsyncStorage.setItem('user_token', jsonValue);
        AsyncStorage.setItem('email', email)
        AsyncStorage.setItem('password', password)
        setUser(res.data);
        console.log(res.data);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setStatus('off')
      });
  };


  return (
    <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
      <Stack.Screen
        options={{
          headerShown: false
        }}
      />
       <Form
        alignItems="center"
        minWidth={'90%'}
        gap="$2"
        onSubmit={() => setStatus('submitting')}
        borderWidth={1}
        borderRadius="$4"
        backgroundColor="$background"
        borderColor="$borderColor"
        padding="$8"
      >
        {/* <H4>{status[0].toUpperCase() + status.slice(1)}</H4> */}
        <Image
          resizeMode="contain"
          style={{width: 160,height:100, justifyContent: 'center'}}
          source={require('../../assets/logo.png')}
        />
        <H4>Welcome to Histreams</H4>
        <YStack width={200} minHeight={150} space="$2">
          <Input value={email} onChangeText={email => setEmail(email)} size="$4" borderWidth={2} placeholder="email"/>
          <Input value={password}  onChangeText={password => setPassword(password)} secureTextEntry={true} size="$4" borderWidth={2} placeholder="password"/>
        </YStack>
        
        <Form.Trigger asChild disabled={status !== 'off'}>
          <Button onTouchStart={() => login()} icon={status === 'submitting' ? () => <Spinner /> : undefined}>
            Sign In
          </Button>
        </Form.Trigger>
      </Form>
      {/* <TouchableOpacity onPress={login}>
        <Text>Login</Text>
        <Text>Login</Text>
      </TouchableOpacity> */}
    </View>
  );
}