import { View,StyleSheet, FlatList } from "react-native";
import { useRouter,Stack, useLocalSearchParams } from 'expo-router';
import React, { useCallback, useRef, useEffect,  useState } from 'react';
import { Paragraph, SizableText, Text, XStack, Spinner, Image, Card, useTheme } from 'tamagui'
import {
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import Ionicons from '@expo/vector-icons/Ionicons';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { widths } from "@tamagui/config";

export default function Shows() {
  const router = useRouter();
  const theme = useTheme()
  const insets = useSafeAreaInsets();
  const background = theme.background.get()

  const [showsListData, setShowsListData] = useState<Array<show>>([]);

  const params = useLocalSearchParams();
  const { id = 0, } = params;


  type show = {
    title?: string;
    retrieveItems?: Array<showEvent>;
  };

  type showEvent = {
    id?: string;
    title?: string;
    pictureUrl?: string;
  };

  useEffect(() => {
    renderShowsDetail(params.id);
  }, []);


  async function renderShowsDetail(id:any) {
    const jsonValue = await AsyncStorage.getItem('user_token');
    const json = jsonValue != null ? JSON.parse(jsonValue) : null;
    axios({
      method: 'post',
      url: '/api/shows_list',
      data: { id: id },
      headers: {
        Authorization: 'Bearer ' + json.access_token,
      },
    })
      .then((response) => {
        const dataSource: Array<show> = [];
        const array: Array<Record<string, any>> = response.data['containers'];
        array.map((s) => {
          if (s.layout == 'horizontal_thumbnail') {
            const a: show = {};
            a.title = s.metadata['label'].toString();
            const ra: Array<showEvent> = [];
            const rarray: Array<Record<string, any>> =
              s.retrieveItems['resultObj']['containers'];
            rarray.map((r) => {
              const ri: showEvent = {};
              ri.id = r.id.toString();
              ri.pictureUrl = r.metadata['pictureUrl'].toString();
              ri.title = r.metadata['title'].toString();
              ra.push(ri);
            });
            a.retrieveItems = ra;
            dataSource.push(a);
          }
          if (s.layout == 'vertical_thumbnail') {
            const a: show = {};
            a.title = s.metadata['label'].toString();
            const ra: Array<showEvent> = [];
            const rarray: Array<Record<string, any>> =
              s.retrieveItems['resultObj']['containers'];
            rarray.map((r) => {
              const ri: showEvent = {};
              ri.id = r.id.toString();
              ri.pictureUrl = r.metadata['pictureUrl'].toString();
              ri.title = r.metadata['title'].toString();
              ra.push(ri);
            });
            a.retrieveItems = ra;
            dataSource.push(a);
          }
        });
        setShowsListData(dataSource);
      })
  }

  function pushToPlayer(id:String) {
    router.push({pathname:'/pages/player', params:{id: id}})
  }


  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingTop: 22,
    },
    item: {
      padding: 5,
      fontSize: 18,
    },
  });
  
  return (
    <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
      <Stack.Screen
        options={{
          title: '',
          headerTintColor: '#fff',
          headerStyle: { backgroundColor: '#202020',  },
          headerBackTitle: 'Home'
        }}
      />
      { showsListData.length == 0 && <Spinner size="large" color="$blue11" />}
      { showsListData.length > 0 && <FlatList
        style={{ backgroundColor: background}}
        data={showsListData}
        renderItem={({item}) => 
        <View style={ {marginBottom: 10,  marginRight: 10, height: 170,}}>
          <Text fontFamily="$body" style={ {margin:4, fontSize: 18}}>{item.title!}</Text>
          <FlatList
            data={item.retrieveItems!}
            horizontal={true}
            renderItem={({item}) => 
            <View key={item.id!}  style={ {marginBottom: 10, marginLeft: 10, marginRight: 10,}}>
              <Card  style={{width: 200,
                      height: 140, overflow: "hidden"}} onPress={() =>  pushToPlayer(item.id!)}>
                <Card.Header padded></Card.Header>
                <Card.Footer >
                  <Text fontFamily="$body" textShadowColor="black" textShadowRadius={3.1}  textShadowOffset={{width:2, height:2}} style={ {margin:4,}}>{item.title!}</Text>
                </Card.Footer>
                <Card.Background>
                  <Image
                    resizeMode="contain"
                    alignSelf="center"
                    source={{
                      width: 200,
                      height: 110,
                      uri: 'https://f1tv.formula1.com/image-resizer/image/' +
                      item.pictureUrl +
                      '?w=700&h=400&q=HI&o=L'
                    }}
                  />
                </Card.Background>
              </Card>
            </View>}
          />
        </View>}
      />}
    </View>
    
  );
}

