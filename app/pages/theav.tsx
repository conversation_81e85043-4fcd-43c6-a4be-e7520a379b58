import { View,StyleSheet, FlatList, Platform } from "react-native";
import { useRouter,Stack, useLocalSearchParams } from 'expo-router';
import React, { useCallback, useRef, useEffect,  useState } from 'react';
import { Paragraph, SizableText, Text, Button, Spinner, Image, Card, useTheme } from 'tamagui'
import {
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import Ionicons from '@expo/vector-icons/Ionicons';
import axios from 'axios';
import { Input } from 'tamagui';
import AsyncStorage from '@react-native-async-storage/async-storage';
import dayjs from 'dayjs';

export default function Theav() {
  const router = useRouter();
  const theme = useTheme()
  const insets = useSafeAreaInsets();
  const background = theme.background.get()
  const params = useLocalSearchParams();
  const { id = 0, } = params;


  const [listData, setListData] = useState<Array<avEvent>>([]);
  const [loading, setLoading] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('中文');
  const [page, setPage] = useState(0);
  const [total, setTotal] = useState(1);

  type avEvent = {
    text?: string;
    cover?: string;
    alt?: string;
    href?: string;
    tag?: string;
  };

  useEffect(() => {
    renderCard(0, searchKeyword);
  }, []);



  async function renderCard(p: number,searchKeyword: string) {
    const jsonValue = await AsyncStorage.getItem('user_token');
    const json = jsonValue != null ? JSON.parse(jsonValue) : null;
    if (!searchKeyword || searchKeyword.length == 0) {
      return;
    }
    let cp = p+1
    setPage(cp)
    axios({
      method: 'post',
      url: 'https://miss.histreams.net/api/miss_list',
      data: { id: searchKeyword, page: cp },
      headers: {
        Authorization: 'Bearer ' + json.access_token,
      },
    }).then((response) => {
      const dataSource: Array<avEvent> = cp == 1 ? [] : [...listData];
      const array: Array<Record<string, any>> = response.data.list;
      if (array) {
        array.map((i) => {
          const a: avEvent = i;
          a.text = a.text ? a.text.replace(/\n/g, '') : '';
          a.tag = a.tag ? a.tag.replace(/\n/g, '') : '';
          dataSource.push(a);
        });
      }
      setListData(dataSource);
      setLoading(false);
      const total = response.data.total
      setTotal(total)
    });
  }

  function handleLoadMore() {
    if (page < total) {
      renderCard(page, searchKeyword);
    }
  }

  function handleSearchKeyword(keyword: string) {
    
    setSearchKeyword(keyword);
    console.log(keyword);
    renderCard(0, searchKeyword);
  }

  function pushToPlayer(id:string) {
    axios({
      method: 'post',
      url: 'https://miss.histreams.net/api/miss_url',
      data: { url: id },
    }).then((response) => {
      router.push({pathname:'/pages/player', params:{id: encodeURIComponent(response.data.url), type: 14}})

    });
    
  }



  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingTop: 22,
    },
    item: {
      padding: 5,
      fontSize: 18,
    },
  });
  
  return (
    <View style={{ flex: 1, alignItems: "center",  }}>
      <Stack.Screen
        options={{
          title: '',
          headerTitle:()=> <Text fontFamily="$body" fontSize={26}>18+</Text>,
          headerTintColor: '#fff',
          headerStyle: { backgroundColor: '#202020',  },
          headerBackTitle: 'Home'
        }}
      />
      <Input 
        style={{ width: '100%', margin: '3%'}}  
        onChangeText={keyword => setSearchKeyword(keyword)} 
        onEndEditing={() => handleSearchKeyword(searchKeyword)} 
        size="$4" 
        borderWidth={2} 
        placeholder="搜索番号或演员名字"
      />
      {/* { listData.length == 0 && <Spinner size="large" color="$blue11" />} */}
      { listData.length > 0 && <FlatList
        style={{ backgroundColor: background, marginLeft: '2%'}}
        data={listData}
        numColumns={2}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        
        renderItem={({item}) => 
        <View style={ {marginBottom: 10,  marginRight: '2%', width: '48%'}}>
          <Card  style={{height: 162, overflow: "hidden" }} onPress={() => {
             pushToPlayer(item.href!);
          }}>
            <Card.Header padded></Card.Header>
            <Card.Footer style={{display: 'flex', flexDirection:'column'}} >
              {/* <Text fontFamily="$body" numberOfLines={1} style={ { marginLeft:4, }}>{item.alt!}</Text> */}
              <Text fontFamily="$body" numberOfLines={1} style={ { marginLeft:0, backgroundColor:'green', width:70, textAlign:'center', borderRadius:5, overflow:"hidden" }}>{item.tag!}</Text> 
              <Text fontFamily="$body" numberOfLines={2} style={ { marginLeft:4, }}>{ `${item.text!}`}</Text>
            </Card.Footer>
            <Card.Background>
              <Image
                resizeMode="cover"
                style={{ width: 170, height: 120 }}
                source={{
                  uri: item.cover
                }}
              />
            </Card.Background>
          </Card>
        </View>}
      />}
    </View>
    
  );
}


