import { View,StyleSheet, FlatList, Image } from "react-native";
import { useRouter,Stack, useLocalSearchParams } from 'expo-router';
import React, { useCallback, useRef, useEffect,  useState } from 'react';
import { Paragraph, SizableText, Text, XStack, Spinner, Card, useTheme } from 'tamagui'
import {
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import Ionicons from '@expo/vector-icons/Ionicons';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function Archive() {
  const router = useRouter();
  const theme = useTheme()
  const insets = useSafeAreaInsets();
  const background = theme.background.get()

  const params = useLocalSearchParams();
  const { id = 0, } = params;


  const [listData, setData] = useState<Array<yearSession>>([]);

  type yearSession = {
    id?: string;
    title?: string;
    pictureUrl?: string;
    pid?: string;
  };

  useEffect(() => {
    renderCard();
  }, []);


  async function renderCard() {
    const jsonValue = await AsyncStorage.getItem('user_token');
    const json = jsonValue != null ? JSON.parse(jsonValue) : null;
    const dataSource: Array<yearSession> = [];
    axios({
      method: 'get',
      url: '/api/season',
      headers: {
        Authorization: 'Bearer ' + json.access_token,
      },
    })
      .then((response) => {
        const array: Array<Record<string, any>> = response.data;
        array.map((year) => {
          const a: yearSession = {};
          a.id = year.cid.toString();
          a.pictureUrl = year.pictureUrl.toString();
          a.title = year.title.toString();
          a.pid = year.pid.toString();
          dataSource.push(a);
        });
        dataSource.sort(function (x, y) {
          return x.title!.split(' ')[0] > y.title!.split(' ')[0] ? -1 : 1;
        });
        setData(dataSource);
      })
  }

  function pushToSession(id:string, title: string) {
    router.push({pathname:'/pages/f1session', params:{id: id, title: title}})
  }


  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingTop: 22,
    },
    item: {
      padding: 5,
      fontSize: 18,
    },
  });
  
  return (
    <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
      <Stack.Screen
        
        options={{
          title: '',
          headerTitle:()=> <Text fontFamily="$body" fontSize={26}>Archive</Text>,
          headerTintColor: '#fff',
          headerStyle: { backgroundColor: '#202020',  },
          headerBackTitle: 'Home'
        }}
      />
      { listData.length == 0 && <Spinner size="large" color="$blue11" />}
      { listData.length > 0 && <FlatList
        
        style={{ backgroundColor: background, marginLeft: '2%'}}
        data={listData}
        numColumns={2}
        renderItem={({item}) => 
        <View style={ {marginBottom: 10,  marginRight: '2%', width: '48%'}}>
          <Card  style={{
                      height: 144, overflow: "hidden" }} onPress={() =>  pushToSession(item.id!, item.title!)}>
                <Card.Header padded></Card.Header>
                <Card.Footer >
                  <Text fontFamily="$body" style={ {flex:1, margin:4, textAlign:'center'}}>{item.title!}</Text>
                </Card.Footer>
                <Card.Background>
                  <Image
                    resizeMode="center"
                    style={{width: '100%',height:110,}}
                    source={{
                      uri: 'https://f1tv.formula1.com/image-resizer/image/' +
                      item.pictureUrl +
                      '?w=700&h=400&q=HI&o=L'
                    }}
                  />
                </Card.Background>
              </Card>
          
        </View>}
      />}
    </View>
    
  );
}

