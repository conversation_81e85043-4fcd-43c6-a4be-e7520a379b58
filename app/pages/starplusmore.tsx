import { View,StyleSheet, FlatList, Platform } from "react-native";
import { useRouter,Stack, useLocalSearchParams } from 'expo-router';
import React, { useCallback, useRef, useEffect,  useState } from 'react';
import { Paragraph, SizableText, Text, Button, Spinner, Image, Card, useTheme } from 'tamagui'
import {
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import Ionicons from '@expo/vector-icons/Ionicons';
import axios from 'axios';
import { Input } from 'tamagui';
import AsyncStorage from '@react-native-async-storage/async-storage';
import dayjs from 'dayjs';
import DateTimePicker, { DateTimePickerAndroid } from '@react-native-community/datetimepicker';

export default function StarplusMore() {
  const router = useRouter();
  const theme = useTheme()
  const insets = useSafeAreaInsets();
  const background = theme.background.get()
  const [date, setDate] = useState(new Date());
  const params = useLocalSearchParams();
  const { id = 0, } = params;


  const [listData, setListData] = useState<Array<starplusEvent>>([]);
  const [loading, setLoading] = useState(false);

  type starplusEvent = {
    eid?: string;
    mid?: string;
    name?: string;
    sportName?: string;
    secondaryTitle?: string;
    imageUrl?: string;
    startDate?: string;
    endDate?: string;
    sportId?: string;
    encodedFamilyId?: string;
    playbackUrl?: string;
  };

  useEffect(() => {
    renderCard('dead');
  }, []);


  async function renderCard(searchKeyword) {
    const jsonValue = await AsyncStorage.getItem('user_token');
    const json = jsonValue != null ? JSON.parse(jsonValue) : null;
    const dataSource: Array<starplusEvent> = [];
    if (!searchKeyword || searchKeyword.length == 0) {
      return;
    }
    axios({
      method: 'post',
      url: '/api/token',
      data: { type: '5' },
      headers: {
        Authorization: 'Bearer ' + json.access_token,
      },
    }).then((response0) => {
      const token = response0.data['token']; 
      axios.get(`https://star.content.edge.bamgrid.com/svc/search/star/version/5.1/region/AR/audience/k-false,l-true/maturity/1850/language/en/queryType/top/pageSize/30/query/${searchKeyword}`,
      {headers: {
        'authorization': token, 
        'x-dss-feature-filtering': 'true',
        'x-dss-edge-accept': 'vnd.dss.edge+json; version=1',
        'x-bamsdk-version': '9.4.1',
        'x-bamsdk-platform': 'android/google/handset',
        'x-application-version': '2.23.0-rc3.0',
        'x-bamsdk-client-id': 'star-22bcaf0a',
        'user-agent': 'BAMSDK/v9.4.1 (star-22bcaf0a 2.23.0-rc3.0; v5.0/v9.4.0; android; phone) OnePlus PJE110 (PJE110-user 13 N2G47H eng.root.20191015.144423 release-keys; Linux; 13; API 33)'
      }}
    )
      .then((response) => {
        const array: Array<Record<string, any>> = response.data.data.search.hits;
        if (array) {
          array.map((i) => {
            const a: starplusEvent = {};
            const e = i.hit;
            a.name = e.text.title.full.program ? e.text.title.full.program.default.content : e.text.title.full.series.default.content;
            a.imageUrl = e.image.tile["1.78"].video ?  e.image.tile["1.78"].video.default.url : e.image.tile["1.78"].series.default.url;
            a.secondaryTitle = e.type == 'DmcSeries' ? 'Series' : 'Movie';
            a.encodedSeriesId = e.encodedSeriesId;
            a.playbackUrl = e.mediaMetadata.playbackUrls.length > 0 ? e.mediaMetadata.playbackUrls[0].href : '';
            dataSource.push(a);
          });
        }
        setListData(dataSource);
      })
    
    })

    
  }

  function formatDate(sessionStartDate: string) {
    const s = dayjs(sessionStartDate).format('MM-DD HH:mm');
    return s;
  }

  function pushToPlayer(id:string) {
    console.log(id)
    router.push({pathname:'/pages/player', params:{id: encodeURIComponent(id), type: 6}})
  }


  function pushToSeries(id:string) {
    console.log(id)
    router.push({pathname:'/pages/starplusseries', params:{id: id}})
  }
  

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingTop: 22,
    },
    item: {
      padding: 5,
      fontSize: 18,
    },
  });
  
  return (
    <View style={{ flex: 1, alignItems: "center",  }}>
      <Stack.Screen
        options={{
          title: '',
          headerTitle:()=> <Text fontFamily="$body" fontSize={26}>Star+</Text>,
          headerTintColor: '#fff',
          headerStyle: { backgroundColor: '#202020',  },
        }}
      />
      <Input style={{ width: '100%', margin: '3%'}}  onChangeText={keyword => renderCard(keyword)} size="$4" borderWidth={2} placeholder="Please input keyword (english only)"/>
      {/* { listData.length == 0 && <Spinner size="large" color="$blue11" />} */}
      { listData.length > 0 && <FlatList
        
        style={{ backgroundColor: background, marginLeft: '2%'}}
        data={listData}
        numColumns={2}
        renderItem={({item}) => 
        <View style={ {marginBottom: 10,  marginRight: '2%', width: '48%'}}>
          <Card  style={{height: 154, overflow: "hidden" }} onPress={() => {
            if (item.secondaryTitle == 'Movie') {
              pushToPlayer(item.playbackUrl);
            } else {
              pushToSeries(item.encodedSeriesId);
            }
            
          }}>
            <Card.Header padded></Card.Header>
            <Card.Footer style={{display: 'flex', flexDirection:'column'}} >
              <Text fontFamily="$body" numberOfLines={1} style={ { marginLeft:4, }}>{item.name!}</Text>
              <Text fontFamily="$body" numberOfLines={1} style={ { marginLeft:4, }}>{ `${item.secondaryTitle!}`}</Text>
            </Card.Footer>
            <Card.Background>
              <Image
                resizeMode="contain"
                alignSelf="center"
                source={{
                  width: 200,
                  height: 110,
                  uri: item.imageUrl + '/scale?width=800&aspectRatio=1.78&format=jpeg'
                }}
              />
            </Card.Background>
          </Card>
        </View>}
      />}
    </View>
    
  );
}


