import { View,StyleSheet, FlatList, Platform } from "react-native";
import { useRouter,Stack, useLocalSearchParams } from 'expo-router';
import React, { useCallback, useRef, useEffect,  useState } from 'react';
import { Paragraph, SizableText, Text, Button, Spinner, Image, Card, useTheme } from 'tamagui'
import {
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import Ionicons from '@expo/vector-icons/Ionicons';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import dayjs from 'dayjs';
import DateTimePicker, { DateTimePickerAndroid } from '@react-native-community/datetimepicker';

export default function Eurosport() {
  const router = useRouter();
  const theme = useTheme()
  const insets = useSafeAreaInsets();
  const background = theme.background.get()
  const [date, setDate] = useState(new Date());
  const params = useLocalSearchParams();
  const { id = 0, } = params;


  const [listData, setListData] = useState<Array<eurosportEvent>>([]);

  type eurosportEvent = {
    id?: string;
    name?: string;
    sportName?: string;
    secondaryTitle?: string;
    imageUrl?: string;
    scheduleStart?: string;
  };

  useEffect(() => {
    renderCard(dayjs(date).format('YYYY-MM-DD'));
  }, []);

  const onChange = (event: any, selectedDate: any) => {
    console.log(selectedDate)
    setDate(selectedDate);
    if (Platform.OS == 'android') {
      console.log(selectedDate)
      setListData([]);
      renderCard(dayjs(selectedDate).format('YYYY-MM-DD'));
    } else {
      if (event['type'] == 'dismissed' ) {
        // const currentDate = selectedDate;
        setListData([]);
        renderCard(dayjs(date).format('YYYY-MM-DD'));
      }
    }
  };

  const showDateTimePickerAndroid = () => {
    DateTimePickerAndroid.open({
      value: date,
      maximumDate: new Date(),
      onChange,
      mode: 'date',
      is24Hour: true,
    });
  };


  async function renderCard(datestr: string) {
    console.log(datestr)
    const jsonValue = await AsyncStorage.getItem('user_token');
    const json = jsonValue != null ? JSON.parse(jsonValue) : null;
    const dataSource: Array<eurosportEvent> = [];
    axios({
      method: 'post',
      url: '/api/ms_stream_schedule',
      data: { type: "discovery" },
      headers: {
        Authorization: 'Bearer ' + json.access_token,
      },
    })
      .then((response) => {
        const array: Array<Record<string, any>> = response.data;
        if (array) {
          array.map((e) => {
            const a: eurosportEvent = {};
              a.sportName = e['category'];
              a.scheduleStart = e['startTime'].toString();
              a.secondaryTitle = e['description'];
              a.name = e['title'].toString();
              a.imageUrl = e['imageUrl'].toString();
              a.id = e['id'].toString();
              dataSource.push(a);
          });
        }
        dataSource.sort(function (x, y) {
          return dayjs(x.scheduleStart) < dayjs(y.scheduleStart) ? -1 : 1;
        });
        setListData(dataSource);
      })
  }

  function formatDate(sessionStartDate: string) {
    const s = dayjs(sessionStartDate).format('MM-DD HH:mm');
    return s;
  }

  function pushToPlayer(id:string, title: string) {
    console.log(id)
    router.push({pathname:'/pages/player', params:{id: id, type: 5}})
  }


  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingTop: 22,
    },
    item: {
      padding: 5,
      fontSize: 18,
    },
  });
  
  return (
    <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
      <Stack.Screen
        options={{
          title: '',
          headerTitle:()=> <Text fontFamily="$body" fontSize={26}>Eurosport</Text>,
          headerTintColor: '#fff',
          headerStyle: { backgroundColor: '#202020',  },

          // headerRight:  ()=> Platform.OS == 'ios' ?  <DateTimePicker
          //     testID="dateTimePicker"
          //     maximumDate={new Date()}
          //     value={date}
          //     mode='date'
          //     onChange={onChange}
          //   /> : <Button onPress={showDateTimePickerAndroid}>{dayjs(date).format('MM-DD')}</Button> ,
        }}
      />
  
      { listData.length == 0 && <Spinner size="large" color="$blue11" />}
      { listData.length > 0 && <FlatList
        
        style={{ backgroundColor: background, marginLeft: '2%'}}
        data={listData}
        numColumns={2}
        renderItem={({item}) => 
        <View style={ {marginBottom: 10,  marginRight: '2%', width: '48%'}}>
          <Card  style={{height: 184, overflow: "hidden" }} onPress={() => dayjs(item.scheduleStart!).valueOf() < Date.now() ? pushToPlayer(item.id!, item.name!):{}}>
            <Card.Header padded></Card.Header>
            <Card.Footer style={{display: 'flex', flexDirection:'column'}} >
              <Text fontFamily="$body" numberOfLines={1} style={ { marginLeft:4, }}>{item.name!}</Text>
              <Text fontFamily="$body" numberOfLines={1} style={ { marginLeft:4, }}>{ `${item.sportName!} | ${item.secondaryTitle!}`}</Text>
              <Text fontFamily="$body" style={ { marginLeft:4, }}>{formatDate(item.scheduleStart!)}</Text>
            </Card.Footer>
            <Card.Background>
              <Image
                resizeMode="contain"
                alignSelf="center"
                opacity={dayjs(item.scheduleStart!).valueOf() < Date.now() ? 1 : 0.5 }
                source={{
                  width: 200,
                  height: 110,
                  uri: item.imageUrl
                }}
              />
            </Card.Background>
          </Card>
        </View>}
      />}
    </View>
    
  );
}

