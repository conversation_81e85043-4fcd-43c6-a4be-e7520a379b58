import { View,StyleSheet, FlatList, Platform } from "react-native";
import { useRouter,Stack, useLocalSearchParams } from 'expo-router';
import React, { useCallback, useRef, useEffect,  useState } from 'react';
import { Paragraph, SizableText, Text, Button, Spinner, Image, Card, useTheme, Select } from 'tamagui'
import type { SelectProps, TabsContentProps } from 'tamagui'
import { Adapt, Label, Sheet, XStack, YStack, Tabs, Separator } from 'tamagui'
import {
  useSafeAreaInsets,
} from 'react-native-safe-area-context';

import Ionicons from '@expo/vector-icons/Ionicons';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import dayjs from 'dayjs';
import DateTimePicker, { DateTimePickerAndroid } from '@react-native-community/datetimepicker';

export default function Starplus() {
  const router = useRouter();
  const theme = useTheme()
  const insets = useSafeAreaInsets();
  const background = theme.background.get()
  const [date, setDate] = useState(new Date());
  const params = useLocalSearchParams();
  const { id = 0, } = params;
  const [tabId, setTabId] = useState('tab1')
  const [liveId, setLiveId] = useState('415311fd-599b-460e-8006-80a3e626c119')

  const [replayId, setReplayId] = useState('70de45d0-d56e-4e2d-9cf3-8f8494b3e012')

  const [listData, setListData] = useState<Array<starplusEvent>>([]);

  type starplusEvent = {
    eid?: string;
    mid?: string;
    name?: string;
    sportName?: string;
    secondaryTitle?: string;
    imageUrl?: string;
    startDate?: string;
    endDate?: string;
    sportId?: string;
    encodedFamilyId?: string;
    playbackUrl?: string;
  };

  useEffect(() => {
    renderCard(liveId, tabId);
  }, []);


  async function renderCard(id: string, currentTabId: string) {
    const jsonValue = await AsyncStorage.getItem('user_token');
    const json = jsonValue != null ? JSON.parse(jsonValue) : null;
    const dataSource: Array<starplusEvent> = [];
    // https://star.content.edge.bamgrid.com/svc/content/CuratedSet/version/5.1/region/AR/audience/k-false,l-true/maturity/1850/language/en/setId/${id}/pageSize/60/page/1
    // https://star.content.edge.bamgrid.com/svc/content/CuratedSet/version/5.1/region/AR/audience/k-false,l-true/maturity/1850/language/en/setId/${id}/pageSize/60/page/1
    axios.get(`https://star.content.edge.bamgrid.com/svc/content/CuratedSet/version/5.1/region/AR/audience/k-false,l-true/maturity/1850/language/en/setId/${id}/pageSize/60/page/1`,
      {headers: {
        'x-dss-feature-filtering': 'true',
        'x-dss-edge-accept': 'vnd.dss.edge+json; version=1',
        'x-bamsdk-version': '9.4.1',
        'x-bamsdk-platform': 'android/google/handset',
        'x-application-version': '2.23.0-rc3.0',
        'x-bamsdk-client-id': 'star-22bcaf0a',
        'user-agent': 'BAMSDK/v9.4.1 (star-22bcaf0a 2.23.0-rc3.0; v5.0/v9.4.0; android; phone) OnePlus PJE110 (PJE110-user 13 N2G47H eng.root.20191015.144423 release-keys; Linux; 13; API 33)'
      }}
    )
      .then((response) => {
        const array: Array<Record<string, any>> = response.data.data.CuratedSet.items;
        if (array) {
          array.map((e) => {
            const a: starplusEvent = {};
            if (currentTabId == 'tab1') {
              if ( e.linear == false) {
                a.name = e.text.title.brief ? e.text.title.brief.program.default.content : e.text.title.full.program.default.content;
                a.imageUrl = e.image.tile["1.78"].program.default.url;
                a.startDate = e.startDate;
                a.secondaryTitle = e.league && e.league.text.name.full.league.default.content ? e.league.text.name.full.league.default.content:undefined;
                a.sportName = e.sport ? e.sport.text.name.full.sport.default.content : undefined;
                a.eid = e.eventId;
                a.mid = e.mediaMetadata.mediaId;
                a.sportId = e.sport ? e.sport.sportId : '';
                a.encodedFamilyId = e.family.encodedFamilyId;
                a.playbackUrl = e.mediaMetadata.playbackUrls[0].href
                dataSource.push(a);
              }
              dataSource.sort(function (x, y) {
                return dayjs(x.startDate) < dayjs(y.startDate) ? -1 : 1;
              });
            } else {
              a.name = e.text.title.brief ? e.text.title.brief.program.default.content : e.text.title.full.program.default.content;
              a.imageUrl = e.image.tile ? e.image.tile["1.78"].program.default.url : e.image.tile["0.71"].program.default.url;
              a.startDate = e.startDate ? e.startDate : e.releases[0].releaseDate;
              a.secondaryTitle = e.league && e.league.text.name.full? e.league.text.name.full.league.default.content:'';
              a.sportName = e.sport ? e.sport.text.name.full.sport.default.content : '';
              a.eid = e.eventId;
              a.mid = e.mediaMetadata.mediaId;
              a.playbackUrl = e.mediaMetadata.playbackUrls[0].href
              dataSource.push(a);
            }
            
          });
        }
        
        setListData(dataSource);
      })
  }

  async function encodedFamilyId(id:string) {
    const jsonValue = await AsyncStorage.getItem('user_token');
    const json = jsonValue != null ? JSON.parse(jsonValue) : null;
    axios({
      method: 'post',
      url: '/api/token',
      data: { type: '5' },
      headers: {
        Authorization: 'Bearer ' + json.access_token,
      },
    }).then((response0) => {
      const token = response0.data['token'];
      axios.get(`https://star.content.edge.bamgrid.com/svc/content/DmcProgramBundle/version/5.1/region/AR/audience/k-false,l-true/maturity/1850/language/en/encodedFamilyId/${id}`,
    {headers: {'Authorization': `Bearer ${token}`}})
      .then((response) => {
        const e: Record<string, any> = response.data.data.DmcProgramBundle.airing;
        const playbackUrl = e.mediaMetadata.playbackUrls[0].href
        pushToPlayer(`${playbackUrl}`);
      });
    
    })
    
  }

  function formatDate(sessionStartDate: string) {
    const s = dayjs(sessionStartDate).format('MM-DD HH:mm');
    return s;
  }

  function pushToPlayer(id:string) {
    console.log(id)
    router.push({pathname:'/pages/player', params:{id: encodeURIComponent(id), type: 6}})
  }

  function selectOnValueChange(id:string) {
    console.log(id)
    if (tabId == 'tab1') {
      setLiveId(id)
    }
    if (tabId == 'tab2') {
      setReplayId(id)
    } 
    setListData([])
    renderCard(id, tabId)
  }
  function tabsOnValueChange(id:string) {
    console.log(id)
    setTabId(id)
    setListData([])
    if (id == 'tab1') {
      renderCard(liveId, id)
    } else {
      renderCard(replayId, id)
    }
    
  }

  


  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingTop: 22,
    },
    item: {
      padding: 5,
      fontSize: 18,
    },
  });
  
  return (
    <View style={{ }}>
      
      <Stack.Screen
        options={{
          title: '',
          headerTitle:()=> <Text fontFamily="$body" fontSize={26}>Star+</Text>,
          headerTintColor: '#fff',
          headerStyle: { backgroundColor: '#202020',  },
          headerRight:  ()=> tabId == 'tab1' ? <SelectDemoItem id='1' native onValueChange={selectOnValueChange} /> : <SelectDemoItem id='2' native onValueChange={selectOnValueChange} />
        }}
      />
      <Tabs
        defaultValue="tab1"
        orientation="horizontal"
        flexDirection="column"
        style={{ padding: '2%'}}
        // width={400}
        value={tabId}
        onValueChange={tabsOnValueChange}
        borderRadius="$4"
        borderWidth="$0.25"
        overflow="hidden"
        borderColor="$borderColor"
      >
      <Tabs.List
        separator={<Separator vertical />}
        disablePassBorderRadius="bottom"
        aria-label="Manage your account"
      >
        <Tabs.Tab flex={1} value="tab1">
          <SizableText fontFamily="$body">Live</SizableText>
        </Tabs.Tab>
        <Tabs.Tab flex={1} value="tab2">
          <SizableText fontFamily="$body">Replays</SizableText>
        </Tabs.Tab>
      </Tabs.List>
      </Tabs>
      { listData.length == 0 && <Spinner size="large" color="$blue11" />}
      { listData.length > 0 && <FlatList
        
        style={{ backgroundColor: background, marginLeft: '2%'}}
        data={listData}
        numColumns={2}
        renderItem={({item}) => 
        <View style={ {marginBottom: 10,  marginRight: '2%', width: '48%'}}>
          <Card  style={{height: 184, overflow: "hidden" }} onPress={() => {
            if (tabId == 'tab1') {
              if (dayjs(item.startDate!).valueOf() < Date.now()) {
                if (item.sportId == 'b1722be4-29e1-4cbb-b445-e9272d304e36') {
                  encodedFamilyId(item.encodedFamilyId!);
                } else {
                  pushToPlayer(item.playbackUrl!)
                }
              }
            } else {
              pushToPlayer(item.playbackUrl!)
            }
          }}>
            <Card.Header padded></Card.Header>
            <Card.Footer style={{display: 'flex', flexDirection:'column'}} >
              <Text fontFamily="$body" numberOfLines={1} style={ { marginLeft:4, }}>{item.name!}</Text>
              <Text fontFamily="$body" numberOfLines={1} style={ { marginLeft:4, }}>{ `${item.sportName!} | ${item.secondaryTitle!}`}</Text>
              <Text fontFamily="$body" style={ { marginLeft:4, }}>{formatDate(item.startDate!)}</Text>
            </Card.Footer>
            <Card.Background>
              <Image
                resizeMode="contain"
                alignSelf="center"
                opacity={dayjs(item.startDate!).valueOf() < Date.now() ? 1 : 0.5 }
                source={{
                  width: 200,
                  height: 110,
                  uri: item.imageUrl + '/scale?width=800&aspectRatio=1.78&format=jpeg'
                }}
              />
            </Card.Background>
          </Card>
        </View>}
      />}
    </View>
    
  );
}


export function SelectDemoItem(props: SelectProps) {
  const [val, setVal] = useState(props.id! == '1' ?'415311fd-599b-460e-8006-80a3e626c119' : '70de45d0-d56e-4e2d-9cf3-8f8494b3e012')

  return (
    <Select value={val} style={{textAlign:'center'}} onValueChange={setVal} disablePreventBodyScroll {...props}>
      <Select.Trigger style={{textAlign:'center'}} width={140}>
        <Select.Value placeholder="Something" />
      </Select.Trigger>
      <Adapt when="sm" platform="touch">
        <Sheet
          native={!!props.native}
          modal
          dismissOnSnapToBottom
          animationConfig={{
            type: 'spring',
            damping: 20,
            mass: 1.2,
            stiffness: 250,
          }}
        >
          <Sheet.Frame>
            <Sheet.ScrollView>
              <Adapt.Contents />
            </Sheet.ScrollView>
          </Sheet.Frame>
          <Sheet.Overlay
            animation="lazy"
            enterStyle={{ opacity: 0 }}
            exitStyle={{ opacity: 0 }}
          />
        </Sheet>
      </Adapt>

      <Select.Content zIndex={200000}>
        <Select.ScrollUpButton
          alignItems="center"
          justifyContent="center"
          position="relative"
          width="100%"
          height="$3"
        >
          <YStack zIndex={10}>
          </YStack>
        </Select.ScrollUpButton>

        <Select.Viewport
          minWidth={200}
        >
          <Select.Group>
            {/* for longer lists memoizing these is useful */}
            {props.id == '1' && liveItems.map((item, i) => {
                return (
                  <Select.Item
                    index={i}
                    key={item.id}
                    value={item.id.toLowerCase()}
                  >
                    <Select.ItemText  style={{textAlign:'center'}}>{item.name}</Select.ItemText>
                    <Select.ItemIndicator marginLeft="auto">
                    </Select.ItemIndicator>
                  </Select.Item>
                )
              })
            }
            {props.id == '2' && replayItems.map((item, i) => {
                return (
                  <Select.Item
                    index={i}
                    key={item.id}
                    value={item.id.toLowerCase()}
                  >
                    <Select.ItemText  style={{textAlign:'center'}}>{item.name}</Select.ItemText>
                    <Select.ItemIndicator marginLeft="auto">
                    </Select.ItemIndicator>
                  </Select.Item>
                )
              })
            }
          </Select.Group>
          {/* Native gets an extra icon */}
          {props.native && (
            <YStack
              position="absolute"
              right={0}
              top={0}
              bottom={0}
              alignItems="center"
              justifyContent="center"
              width={'$4'}
              pointerEvents="none"
            >
            </YStack>
          )}
        </Select.Viewport>

        <Select.ScrollDownButton
          alignItems="center"
          justifyContent="center"
          position="relative"
          width="100%"
          height="$3"
        >
          <YStack zIndex={10}>
          </YStack>
        </Select.ScrollDownButton>
      </Select.Content>
    </Select>
  )
}

const TabsContent = (props: TabsContentProps) => {
  return (
    <Tabs.Content
      backgroundColor="$background"
      key="tab3"
      padding="$2"
      alignItems="center"
      justifyContent="center"
      flex={1}
      borderColor="$background"
      borderRadius="$2"
      borderTopLeftRadius={0}
      borderTopRightRadius={0}
      borderWidth="$2"
      {...props}
    >
      {props.children}
    </Tabs.Content>
  )
}

const liveItems = [
  { name: 'All', id: '415311fd-599b-460e-8006-80a3e626c119' },
  { name: 'Soccer', id: '7a8697fd-ed8e-449c-a29c-df51623fe22a' },
  { name: 'Premier League', id: '219065e3-0244-4171-baec-0a66caac1b0c' },
  { name: 'La Liga', id: 'cb43b4ce-3245-48b9-b41d-2635b6bb890d' },
  { name: 'Serie A', id: '744d114e-832f-4529-bca8-4b341f5aecc4' },
  { name: 'Bundesliga', id: 'ee2b2599-a217-4814-a98b-bbeae54812ef' },
  { name: 'Ligue 1', id: '0cb8d29b-3311-40a8-8e58-b1ceca42509c' },
  { name: 'F1', id: '4fae2047-fee2-4728-a077-85e4c057f59e'},
  { name: 'F2', id: '5105cbc2-f5ce-44a4-bfce-e22b2db61d7b'},
  { name: 'F3', id: '8f938da0-ec0f-4de4-a40a-b7904fd28748'},
  { name: 'MotoGP', id:'435723c7-00a2-4f48-847a-f85eb58f99c3'},
  { name: 'NBA', id: '74f15cd3-9df2-4142-b4fe-ac383ba54be4'},
  { name: 'NFL', id: '8c2906b2-bdea-467c-a95b-715621aaa874'},
  { name: 'NHL', id: '0ac0d444-928c-4225-a907-48405854de6d'},
  { name: 'UFC', id: '25e5c8c2-b965-41c5-a32d-e4842dcb7bee'},
  { name: 'Tennis', id: '493a29f2-6917-4cfa-9f27-881cf9a9e4ea'},
  { name: 'Table Tennis', id: '44cbdddd-44b8-4523-a816-d946da0f96c7'}
]

const replayItems = [
  { name: 'All', id: '70de45d0-d56e-4e2d-9cf3-8f8494b3e012' },
  { name: 'Soccer', id: '3527b76f-d754-4318-9f25-61b7b76d62b1' },
  { name: 'Premier League', id: '65966d95-2088-4585-8f76-2cc1b61cbb24' },
  { name: 'La Liga', id: 'fd8f329a-3285-4524-880a-fc55d94b3c9c' },
  { name: 'Serie A', id: '93252716-95ca-4148-8089-2d53378445a5' },
  { name: 'Bundesliga', id: 'c7988716-deb3-47c8-a496-c92163304a52' },
  { name: 'Ligue 1', id: '9e105468-bd8a-4134-8264-1021559736f8' },
  { name: 'F1', id: 'f1dc11c8-2447-4e57-b143-cc08843a2fe3'},
  { name: 'F2', id: '93bd4c62-c302-4769-9351-97dd96790c99'},
  { name: 'F3', id: 'd0998aee-fae3-403a-9503-a1307e8a8160'},
  { name: 'MotoGP', id:'a0294a3e-83b1-41f0-9c73-b90e2891abb0'},
  { name: 'NBA', id: '12b52b1a-0ff6-4631-ab0c-d7a9ec4da0cf'},
  { name: 'NFL', id: '6956ae7d-3dab-4a19-881f-b0c638befd2e'},
  { name: 'NHL', id: '2da9b719-9b7b-4e0c-9e6a-840d31657aa1'},
  { name: 'UFC', id: '8412921e-d2ad-47b6-90c2-b370d2fc724a'},
  { name: 'Tennis', id: '97d21361-11fb-4bed-80e0-7b52c4fba404'},
  { name: 'Table Tennis', id: '99b07b43-9a1a-47d8-a81a-1d43178208c2'}
]

