import React, { useCallback, useRef, useState, useEffect } from 'react';
import { View, Platform, StyleSheet, StatusBar, FlatList, TouchableOpacity, } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import {
  Event,
  usePlayer,
  PlayerView,
  SourceType,
  FullscreenHandler,
  BitmovinCastManager,
  Source,
  DrmConfig,
  ScalingMode,
  HttpRequestType,
  HttpRequest,
  HttpResponse,
  OfflineContentManager,
  OfflineContentOptions,
  OfflineDownloadRequest,
  OfflineState,
} from 'bitmovin-player-react-native';
import {  Stack, useLocalSearchParams } from 'expo-router';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Buffer } from 'buffer';
import base64 from 'react-native-base64';
import { Sheet } from '@tamagui/sheet'
import {  Text, Spinner, Image, Card, useTheme, Button } from 'tamagui'
import Ionicons from '@expo/vector-icons/Ionicons';
import { useKeepAwake } from 'expo-keep-awake';

import { Svg, Circle, Text as SvgText, Path, TextPath, G } from 'react-native-svg';

import { Alert } from 'react-native';
import { log } from 'console';

function prettyPrint(header: string, obj: any) {
  console.log(header, JSON.stringify(obj, null, 2));
}

class SampleFullscreenHandler implements FullscreenHandler {
  isFullscreenActive: boolean = true;
  onFullscreen: (fullscreenMode: boolean) => void;

  constructor(
    isFullscreenActive: boolean,
    onFullscreen: (fullscreenMode: boolean) => void
  ) {
    this.isFullscreenActive = isFullscreenActive;
    this.onFullscreen = onFullscreen;
  }

  enterFullscreen(): void {
    this.isFullscreenActive = true;
    StatusBar.setHidden(true);
    this.onFullscreen(true);
  }

  exitFullscreen(): void {
    this.isFullscreenActive = false;
    StatusBar.setHidden(false);
    console.log('exit fullscreen');
    this.onFullscreen(false);
  }
}

type stream = {
  racingNumber?: number;
  channelId?: string;
  title?: string;
  playbackUrl?: string;
  reportingName?: string;
  type?: string;
  teamName?: string;
  driverFirstName?: string;
  driverLastName?: string;
  hex?: string;
};

type tokenChannel = {
  id?: number;
  name?: string;
  token?: string;
};

interface Channel {
  0: number;  // RPM
  2: number;  // Speed
  3: number;  // Gear
  4: number;  // Throttle
  5: number;  // Brake
  45: number; // DRS
}

interface Car {
  Channels: Channel;
}

interface CarEntry {
  Utc: string;
  Cars: {
    [key: string]: Car;  // 键是赛车号码(如 "1", "44" 等)
  }
}

interface F1CarData {
  f1LiveTimingClock: {
    paused: boolean;
    systemTime: string;
    trackTime: string;
    liveTimingStartTime: string;
  };
  version: string;
  f1LiveTimingState: {
    CarData: {
      Entries: CarEntry[];
    }
  }
}

// 添加缓存数据的接口
interface CachedEntry {
  Utc: string;
  Channels: Channel;
}

export default function Player() {

  // 添加仪表盘组件
const TelemetryDashboard = ({ carData }: { carData: Channel }) => {
  return (
    <View style={styles.dashboardContainer}>
      <Svg height="200" width="200">
        {/* Speed弧形 (蓝色) */}
        <G>
          {/* 背景弧形（灰色） */}
          <Circle
            cx="100"
            cy="100"
            r="80"
            stroke="rgba(128, 128, 128, 0.3)"
            strokeWidth="15"
            strokeLinecap="round"
            fill="transparent"
            strokeDasharray="419 500"
            transform="rotate(120 100 100)"
          />
          {/* 进度弧形（蓝色） */}
          {carData[2] > 0 && (
            <Circle
              cx="100"
              cy="100"
              r="80"
              stroke="#0066cc"
              strokeWidth="15"
              strokeLinecap="round"
              fill="transparent"
              strokeDasharray={`${(carData[2] / 360) * 419} 500`}
              transform="rotate(120 100 100)"
            />
          )}
        </G>
        
        {/* Throttle弧形和文字 */}
        <G>
          {/* 背景弧形（灰色） */}
          <Circle
            cx="100"
            cy="100"
            r="60"
            stroke="rgba(128, 128, 128, 0.3)"
            strokeWidth="15"
            strokeLinecap="round"
            fill="transparent"
            strokeDasharray="182 502"
            transform="rotate(120 100 100)"
          />
          {/* 进度弧形（绿色） */}
          {carData[4] > 0 && (
            <Circle
              cx="100"
              cy="100"
              r="60"
              stroke="#00cc66"
              strokeWidth="15"
              strokeLinecap="round"
              fill="transparent"
              strokeDasharray={`${(carData[4] / 100) * 182} 502`}
              transform="rotate(120 100 100)"
            />
          )}
          <Path
            id="throttlePath"
            d={`M 43,100 A 60,60 0 0,1 163,100`}
            fill="none"
            stroke="none"
          />
          <SvgText fill="white" fontSize="10">
            <TextPath href="#throttlePath" startOffset="0%">
              THROTTLE
            </TextPath>
          </SvgText>
        </G>
        
        {/* Brake弧形 (红色/灰色) */}
        <G>
          <Circle
            cx="100"
            cy="100"
            r="60"
            stroke={Number(carData[5]) > 0 ? "#cc0000" : "rgba(128, 128, 128, 0.3)"}
            strokeWidth="15"
            strokeLinecap="round"
            fill="transparent"
            strokeDasharray="112 628"
            transform="rotate(310 100 100)"
          />
          <Path
            id="brakePath"
            d={`M 36,100 A 60,60 0 0,1 156,100`}
            fill="none"
            stroke="none"
          />
          <SvgText fill="white" fontSize="10">
            <TextPath href="#brakePath" startOffset="80%">
              BRAKE
            </TextPath>
          </SvgText>
        </G>
        
        {/* 速度显示 */}
        <SvgText
          x="100"
          y="80"
          fontSize="30"
          fill="white"
          textAnchor="middle"
        >
          {Math.round(carData[2])}
        </SvgText>
        
        <SvgText 
          x="100" 
          y="100" 
          fontSize="14" 
          fill="white" 
          textAnchor="middle"
        >
          KMH
        </SvgText>

        {/* RPM显示 */}
        <SvgText
          x="100"
          y="120"
          fontSize="20"
          fill="white"
          textAnchor="middle"
        >
          {Math.round(carData[0])}
        </SvgText>

        <SvgText 
          x="100" 
          y="135" 
          fontSize="14" 
          fill="white" 
          textAnchor="middle"
        >
          RPM
        </SvgText>

        {/* DRS状态 */}
        <SvgText
          x="100"
          y="150"
          fontSize="16"
          fill={carData[45] ?  "#666666" : "#00cc66" }
          textAnchor="middle"
        >
          DRS
        </SvgText>

        {/* 档位显示 */}
        <SvgText
          x="100"
          y="170"
          fontSize="14"
          fill="white"
          textAnchor="middle"
        >
          {`GEAR${carData[3]}`}
        </SvgText>
      </Svg>
    </View>
  );
};

  
  
  useKeepAwake();
  const params = useLocalSearchParams()

  const [open, setOpen] = useState(false)
  const [streamsList, setStreamsListData] = useState<Array<stream>>([])
  const [sourceIsLoading, setSourceIsLoading] = useState(true)
  const { id = 0, type = 0} = params;
  const [refreshButtonShow, setRefreshButtonShow] = useState(false)
  
  const [playerScalingMode, setPlayerScalingMode] = useState(ScalingMode.Fit)
  
  const [tokenChannelSheet, setTokenChannelSheetOpen] = useState(false)
  const [tokenChannelTempUrl, setTokenChannelTempUrl] = useState('')
  const [tokenChannelList, setTokenChannelListData] = useState<Array<tokenChannel>>([])

  const [carData, setCarData] = useState<F1CarData | null>(null);

  const [offlineContentManager, setOfflineContentManager] =
    useState<OfflineContentManager>();
  const [downloadState, setDownloadState] = useState<OfflineState>();
  const initialDownloadRequest: OfflineDownloadRequest = {
    minimumBitrate: 800000,
  };
  const [downloadRequest, setDownloadRequest] =
    useState<OfflineDownloadRequest>(initialDownloadRequest);
  
  let setCookies: string = '';
  let tokenChannelTempEntitlementtoken: string = '';
  let reStreamUrl: string = '';
  let reTimer: NodeJS.Timeout;

  // if (Platform.OS === 'android') {
  //   BitmovinCastManager.initialize();
  //   // Must be called in every activity on Android
  //   BitmovinCastManager.updateContext();
  // }

  const [fullscreenMode, setFullscreenMode] = useState(false);
  const fullscreenHandler = useRef(
    new SampleFullscreenHandler(fullscreenMode, (isFullscreen: boolean) => {
      setFullscreenMode(isFullscreen);
    })
  ).current;


  const requestCallback = (type: HttpRequestType, request: HttpRequest) => {
    // Access current properties
 
    // console.log(JSON.stringify(type));
    // console.log(JSON.stringify(request));
 
    // Modify the request
    if (setCookies.length > 0) {
      // const loginsession = {"data":{"subscriptionToken": tokenChannelToken}}
      // const a = JSON.stringify(loginsession);
      // const loginsessionURI = encodeURIComponent(a);
      // request.headers['cookie'] = `login-session=${loginsessionURI};entitlement_token=${tokenChannelTempEntitlementtoken};${setCookies}`;
      // const result = setCookies.split(";");
      request.headers['cookie'] = setCookies;
    }
    
    // request.method = 'GET';
 
    // Return the processed request via a Promise
    const processed: HttpRequest = {
      body: request.body,
      headers: request.headers,
      method: request.method,
      url: request.url,
    };
    
    if (type == HttpRequestType.ManifestDash) {
      if (reStreamUrl!=undefined && reStreamUrl.length > 0) {
        processed.url = reStreamUrl;
      }
    }
    return Promise.resolve(processed);
  };

  const responseCallback = (type: HttpRequestType, response: HttpResponse) => {
    // Access response properties

    // console.log(JSON.stringify(response));
 
    // Modify the response
    if (response.headers['Set-Cookie'] && setCookies.length == 0) {
      setCookies = response.headers['Set-Cookie'];
    }
    // response.headers['New-Header'] = 'val';
    // response.url = response.request.url; // remove eventual redirect changes
 
    // Return the processed response via a Promise

    return Promise.resolve(response);
  };

  const player = usePlayer({
    licenseKey: 'ad606391-3952-4756-a3bf-91ed018cc5a8',
    remoteControlConfig: {
      sendManifestRequestsWithCredentials: true,
      sendSegmentRequestsWithCredentials: true,
      sendDrmLicenseRequestsWithCredentials: true,
    },
    networkConfig: {
      preprocessHttpRequest: requestCallback,
      preprocessHttpResponse: responseCallback
    },
    tweaksConfig: {
      isCustomHlsLoadingEnabled : false
    }
  });

  

  const getF1UrlData = async (url:string) => {
    try {
      const jsonValue = await AsyncStorage.getItem('user_token');
      const json = jsonValue != null ? JSON.parse(jsonValue) : null;
      // console.log(json)
      axios({
        method: 'post',
        url: '/api/token_v2',
        data: { type: '0' },
        headers: {
          Authorization: 'Bearer ' + json.access_token,
        },
      }).then(async (response) => {
        setTokenChannelTempUrl(url);
        const tokenList = response.data['token'] as Array<tokenChannel>;
        tokenChannelTempEntitlementtoken = response.data['entitlementtoken'];
 
        // console.log(response.data);
        if (tokenList.length > 0) {
          if (tokenList.length == 1) {
            fetchF1SourceUrl(url, tokenList[0].token!)
          } else {
            setTokenChannelListData(tokenList);
            setTokenChannelSheetOpen(true);
          }
        }
        
      });
    } catch (e) {
      // error reading value
      console.log(e);
      
    }
  };
  async function refetchPlayerDetail(url :string) {
    const k = await AsyncStorage.getItem('4k');
      var f1url = `https://f1tv.formula1.com/2.0/R/ENG/TABLET_HLS/ALL/${url}&player=player_bm`;
      var device_info = '';
      if (Platform.OS == 'android') {
        device_info = 'device=android15;screen=tablet;os=android15;model=MiBOX3S;osVersion=15.0;appVersion=2.31.0;playerVersion=3.65.0';
        // device_info = 'device=android;screen=mobile;os=android;model=SM-S931B;osVersion=15';
        // device_info = 'device=rokuos;screen=bigscreen;os=rokuos;model=SM-S931B;osVersion=15';
      } else {
        device_info = 'device=ios;screen=tablet;os=ios;model=ipad13.8;osVersion=17.6;appVersion=2.31.0;playerVersion=3.56.0';
      }

      if (k == '1') {
        f1url = `https://f1tv.formula1.com/2.0/R/ENG/BIG_SCREEN_HLS/ALL/${url}&player=player_bm`;
        device_info = 'device=tvos;screen=bigscreen;os=tvos;model=appletv14.1;osVersion=16.4;appVersion=2.31.0;playerVersion=3.65.0';
      }
      axios({
        method: 'get',
        url: f1url,
        headers: {
          // 'entitlementtoken': tokenChannelTempEntitlementtoken,
          'ascendonToken': tokenChannelList[0].token!,
          'x-f1-device-info': device_info
        },
      })
      .then((response) => {
        console.log(response.data);
        const ro = response.data.resultObj;
        reStreamUrl = ro.url
        
      })
  }

  async function fetchF1SourceUrl(url:string, token: string) {
    setTokenChannelSheetOpen(false);

    console.log(`fetchF1SourceUrl -- ${token}`);
    console.log(`tokenChannelTempEntitlementtoken -- ${tokenChannelTempEntitlementtoken}`);
    if (token && token.includes('histreams.net')) {
      let stype: SourceType = SourceType.HLS;
      const source = new Source({
        url: token,
        type: stype,
        metadata: { platform: Platform.OS },
      });
      player.loadSource(source);
    } else {
      const k = await AsyncStorage.getItem('4k');
      var f1url = `https://f1tv.formula1.com/2.0/R/ENG/TABLET_HLS/ALL/${url}&player=player_bm`;
      var device_info = '';
      if (Platform.OS == 'android') {
        device_info = 'device=android15;screen=tablet;os=android15;model=MiBOX3S;osVersion=15.0;appVersion=2.31.0;playerVersion=3.65.0';
        // device_info = 'device=android;screen=mobile;os=android;model=SM-S931B;osVersion=15';
        // device_info = 'device=rokuos;screen=bigscreen;os=rokuos;model=SM-S931B;osVersion=15';
      } else {
        device_info = 'device=ios;screen=tablet;os=ios;model=ipad13.8;osVersion=17.6;appVersion=2.31.0;playerVersion=3.56.0';
      }

      if (k == '1') {
        f1url = `https://f1tv.formula1.com/2.0/R/ENG/BIG_SCREEN_HLS/ALL/${url}&player=player_bm`;
        device_info = 'device=tvos;screen=bigscreen;os=tvos;model=appletv14.1;osVersion=16.4;appVersion=2.31.0;playerVersion=3.65.0';
      }
      axios({
        method: 'get',
        url: f1url,
        headers: {
          // 'entitlementtoken': tokenChannelTempEntitlementtoken,
          'ascendonToken': token,
          'x-f1-device-info': device_info
        },
      })
      .then((response2) => {
        console.log(response2.data);
        if (JSON.stringify(response2.data['resultObj']) !== '{}') {
          
          tokenChannelTempEntitlementtoken = response2.data['resultObj']['entitlementToken'];
          setPlayerSource(response2.data, token, response2.data['resultObj']['entitlementToken']);
        }
      })
    }
  }


  async function setPlayerSource(response:any, token: any, entitlementToken: any) {
    try {
        let url = '';
        url = response['resultObj']['url']
        let type: SourceType;
        let drmConfig: DrmConfig = {};
        
        if (Platform.OS == 'ios' && (response['resultObj']['drmType'] == 'fairplay' || response['resultObj']['streamType'] == 'MULTICMAFFP' || response['resultObj']['streamType'] == 'CMAFFP')) {
          
          type = SourceType.HLS
          drmConfig.fairplay =  {
            licenseRequestHeaders: {
              'ascendonToken': token,
              'entitlementToken': entitlementToken
            },
            licenseUrl: response['resultObj']['laURL'],
            certificateUrl: 'https://f1tv.formula1.com/fairplay01.der',
            prepareLicense: (license) => {
              console.log('prepareLicense')
              console.log(`${license}`)
              console.log(`${base64.decode(license)}`)
              return base64.decode(license);
            },
            prepareMessage: (message, assetId) => {
              console.log('prepareMessage')
              console.log(`spc=${message}&assetId=${assetId}`)
              const data = Buffer.from(`spc=${message}&assetId=${assetId}`, 'utf-8').toString('base64')
              return data;
            },
          };
        } else if ( response['resultObj']['drmType'] == 'widevine') {
          url = response['resultObj']['url']
          type = SourceType.DASH
          drmConfig.widevine = {
            licenseUrl: response['resultObj']['laURL'],
            // preferredSecurityLevel: 'L1', // L3 = software level Drm protection.
            shouldKeepDrmSessionsAlive: true,
            httpHeaders: {
              'ascendonToken': token,
            },
          }

        } else if (response['resultObj']['streamType'] == 'SDR_HD_DASH') {
          url = response['resultObj']['url']
            type = SourceType.DASH
        } else {
          url = response['resultObj']['url']
          type = SourceType.HLS
        }

        
        const source = new Source({
          url: url,
          type: type,
          drmConfig: drmConfig,
          metadata: { platform: Platform.OS },
       
        });
        // player.loadSource(source);
        // Configure playing DASH source on Chromecast, even when casting from iOS.

        // console.log(response1['resultObj']['streamType']);
        if (response['resultObj']['streamType'] == 'HLS') {
          source.remoteControl = {
            castSourceConfig: {
              url: url,
              type: SourceType.DASH,
              drmConfig: drmConfig,
            },
          };
        }
        
        player.loadSource(source);
        const newOfflineContentManager = new OfflineContentManager({
          identifier: 'STABLE_CONTENT_IDENTIFIER',
          sourceConfig: source.config!,
        });
        console.log('newOfflineContentManager');
        
        const removeOfflineContentManagerListener = newOfflineContentManager.addListener({
          onOptionsAvailable: (e) => {

            console.log(e);
            console.log('Options available');
            // Select desired tracks for download
          },
          onProgress: (e) => {
            console.log(e);
            console.log('onProgress');
          },
        });

        newOfflineContentManager
        .initialize()
        .then(() => {
          setOfflineContentManager(newOfflineContentManager);
          newOfflineContentManager.state().then((state) => {
            setDownloadState(state);
          });
          newOfflineContentManager.getOptions().catch(console.error);
        })
        .catch(console.error);
    } catch (error) {
        console.error('请求失败:', error);
    }
  }

  async function fetchF1StreamsList() {
    const jsonValue = await AsyncStorage.getItem('user_token');
    const json = jsonValue != null ? JSON.parse(jsonValue) : null;
    axios({
      method: 'post',
      url: '/api/f1streams_list',
      data: { id: params.id },
      headers: {
        Authorization: 'Bearer ' + json.access_token,
      },
    })
      .then((response) => {
      //  console.log(response.data['resultObj']['containers'][0]['metadata']['additionalStreams'])
      console.log(response.data['resultObj']['containers'])
       const array: Array<Record<string, any>> =
        response.data['resultObj']['containers'];
        if (array.length > 0) {
          if (Number(array[0].metadata['year']) >= 2025 && array[0].metadata['contentSubtype'] == 'LIVE') {
            // setIsLive(true);
          } else {
            clearInterval(reTimer);
          }
          const dataSource: Array<stream> = [];
          const sarray: Array<Record<string, any>> =
            array[0].metadata['additionalStreams'];
          sarray.map((s) => {
            const a: stream = {};
            a.channelId = s.channelId.toString();
            a.title = s.title.toString();
            a.playbackUrl = s.playbackUrl.toString();
            if (s.teamName) {
              a.racingNumber = Number(s.racingNumber);
              a.reportingName = s.reportingName.toString();
              a.type = s.type.toString();
              a.teamName = s.teamName.toString();
              a.driverFirstName = s.driverFirstName.toString();
              a.hex = s.hex.toString();
            }

            dataSource.push(a);
          });
          setStreamsListData(dataSource);
        }
      })
  }

  async function fetchViaply4K() {
    const jsonValue = await AsyncStorage.getItem('user_token');
    const json = jsonValue != null ? JSON.parse(jsonValue) : null;
    axios({
      method: 'post',
      url: '/api/viaplay_4k_url',
      data: {type: '8'},
      headers: {
        Authorization: 'Bearer ' + json.access_token,
      },
    }).then((response) => {
        // console.log(response.data)

          // let url = 'https://b470cd0b1832440bb2cf8faf68599dbd.mediatailor.us-east-1.amazonaws.com/v1/master/6f3f45fea6332a47667932dede90d20a96f2690c/peacock-hls-vod-4s-generic/pub/global/FER/691/6ef/202404/6916ef3d-c8e2-5101-aa80-7f0958bfe3c6/peacock708641-cbc/master.m3u8?aws.sessionId=74dca921-49e7-49a4-b047-1eedbdf85923';
          // let stype: SourceType = SourceType.HLS;
        
          // const source = new Source({
          //   url: url,
          //   type: stype,
          //   metadata: { platform: Platform.OS },
          // });
          // player.loadSource(source);
          // return


        if (response.data['token']) {
          const response_json = JSON.parse(response.data['token'])
          console.log(response_json.streamUrl);
          console.log(response_json.licenseUrl);
          let url = response_json.streamUrl;
          let stype: SourceType = SourceType.DASH;
          let drmConfig: DrmConfig = {};
          drmConfig.widevine = {
              licenseUrl: response_json.licenseUrl
            }
          const source = new Source({
            url: url,
            type: stype,
            drmConfig: drmConfig,
            metadata: { platform: Platform.OS },
          });
          console.log(source);
          
          // player.loadSource(source);
          // Configure playing DASH source on Chromecast, even when casting from iOS.
      
          // console.log(response1['resultObj']['streamType']);
          source.remoteControl = {
            castSourceConfig: {
              url: url,
              type: stype,
              drmConfig: drmConfig,
            },
          };
          player.loadSource(source);
        } else {
          Alert.alert(
            '',
            JSON.stringify(response.data),
            [
              {
                text: 'close',
                onPress: () =>{{}},
                style: 'destructive',
              },
            ],
            {
              cancelable: true
            },
          );
        }
        
    }).catch( e => {
      console.log(e);
    })
  }

  async function fetchMsStreams() {
    const jsonValue = await AsyncStorage.getItem('user_token');
    const json = jsonValue != null ? JSON.parse(jsonValue) : null;
    axios({
      method: 'post',
      url: '/api/ms_stream_url',
      data: { id: params.id, type: type == '5' ? 'eurosport' : 'viaplay' },
      headers: {
        Authorization: 'Bearer ' + json.access_token,
      },
    })
      .then((response) => {
        console.log(response.data)
        if (response.data['streamUrl']) {
          let url = '';
          let stype: SourceType;
          let drmConfig: DrmConfig = {};
          if (response.data['licenseUrl']) {
            stype = SourceType.DASH
            url = response.data['streamUrl']
            drmConfig.widevine = {
              licenseUrl: response.data['licenseUrl']
            }
          } else {
            url = response.data['streamUrl'] ?? response.data['data']['stream']
            stype = SourceType.HLS
          }

          const source = new Source({
            url: url,
            type: stype,
            drmConfig: drmConfig,
            metadata: { platform: Platform.OS },
          });
          source.remoteControl = {
            castSourceConfig: {
              url: url,
              type: stype,
              drmConfig: drmConfig,
            },
          };
          player.loadSource(source);

       } else {
        if (response.data['message'] && response.data['message'] == 'jwt expired') {
          fetchRefreshMSToken()
         }
       }
      })
  }

  async function fetchRefreshMSToken() {
    const jsonValue = await AsyncStorage.getItem('user_token');
    const json = jsonValue != null ? JSON.parse(jsonValue) : null;
    axios({
      method: 'get',
      url: '/api/refresh_ms_token',
      headers: {
        Authorization: 'Bearer ' + json.access_token,
      },
    }).then((response) => {
      fetchMsStreams()
    })
  }

  function switchStream(playbackUrl:string) {
    setOpen(false)
    getF1UrlData(playbackUrl)
    clearInterval(reTimer)
    reTimer = setInterval(() => {
        refetchPlayerDetail(playbackUrl);
        console.log('reTimer setInterval');
      }, 1000 * 60 * 1);
  }

  function renderStreamsSheet() {
    return <FlatList
      data={streamsList}
      numColumns={3}
      renderItem={({item}) => 
      <TouchableOpacity onPress={() => switchStream(item.playbackUrl!)}  style={ { marginRight: '2%', height: 50, width:'31%', flexDirection:'row', alignItems:'center', justifyContent:'center'}}>
        <View style={{height: 16, width:4, backgroundColor: item.hex, margin:4}}></View>
        <Text fontFamily="$body" textAlign={'center'} style={{margin:4}}>{item.racingNumber}</Text>
        <Text fontFamily="$body" textAlign={'center'}>{item.title}</Text>
      </TouchableOpacity>}
    />
  }

  function renderTokenChannelSheet() {
    return <FlatList
      data={tokenChannelList}
      numColumns={1}
      renderItem={({item}) => 
      <TouchableOpacity onPress={() => fetchF1SourceUrl(tokenChannelTempUrl, item.token!)}  style={ { height: 50, alignItems:'center', justifyContent:'center'}}>
        <Text fontFamily="$body">{item.name}</Text>
      </TouchableOpacity>}
    />
  }


  function fetchBeesport() {
    axios({
      method: 'post',
      url: 'https://miss.histreams.net/api/bee',
      data: { channel: `https://live_tv.starcdnup.com/${params.id}/index.m3u8` }
    }).then((response) => {
      console.log(response.data);
      const url = response.data['channels'][0]
      let stype: SourceType = SourceType.HLS;
      const source = new Source({
        url: url,
        type: stype,
        metadata: { platform: Platform.OS },
      });
      source.remoteControl = {
        castSourceConfig: {
          url: url,
          type: stype,
        },
      };
      player.loadSource(source);

    })
    
  }

  function fetchHami() {
    let url = `https://x.histreams.net/hami/${params.id}`;
    let stype: SourceType = SourceType.HLS;
    const source = new Source({
      url: url,
      type: stype,
      metadata: { platform: Platform.OS },
    });
    source.remoteControl = {
      castSourceConfig: {
        url: url,
        type: stype,
      },
    };
    player.loadSource(source);
  }

  function fetchTheav(url:string) {
    let stype: SourceType = SourceType.HLS;
    const source = new Source({
      url: url,
      type: stype,
      metadata: { platform: Platform.OS },
    });
    source.remoteControl = {
      castSourceConfig: {
        url: url,
        type: stype,
      },
    };
    player.loadSource(source);
  }

  useEffect(() => {

    if (type == '0') {
      fetchF1StreamsList()
      getF1UrlData(`CONTENT/PLAY?contentId=${params.id}`)
      reTimer = setInterval(() => {
        refetchPlayerDetail(`CONTENT/PLAY?contentId=${params.id}`);
        console.log('reTimer setInterval');
      }, 1000 * 60 * 1);
      
    } else if (type == '20') {
      // getF1UrlData(`${params.id}`)
    } else if (type == '8') {
      fetchViaply4K()
    } else if (type == '12') {
      fetchBeesport()
    } else if (type == '13') {
      fetchHami()
    } else if (type == '14') {
      const idurl: string = decodeURIComponent(params.id as string)
      fetchTheav(idurl)
    }else {
      fetchMsStreams()
    }
    
  }, []);

  // 添加缓存数据的状态
  const [cachedEntries, setCachedEntries] = useState<CachedEntry[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);

  const fetchLiveTimingData = async () => {
    try {
      const response = await axios.post('http://192.168.124.27:10101/api/graphql', {
        query: `
          query ExampleQuery {
            f1LiveTimingState {
              CarData
            }
            f1LiveTimingClock {
              liveTimingStartTime
            }
          }
        `
      });
      
      if (response.data?.data?.f1LiveTimingState?.CarData?.Entries) {
        const entries = response.data.data.f1LiveTimingState.CarData.Entries;
        
        // // 处理新的数据条目
        // const newEntries = entries
        //   .filter(entry => entry.Cars[params.racingNumber])
        //   .map(entry => ({
        //     Utc: entry.Utc,
        //     Channels: entry.Cars[params.racingNumber].Channels
        //   }));

        // // 合并新旧数据并去重
        // setCachedEntries(prevEntries => {
        //   const combined = [...prevEntries, ...newEntries];
        //   const unique = combined.filter((entry, index, self) =>
        //     index === self.findIndex(e => e.Utc === entry.Utc)
        //   );
        //   // 按 Utc 排序
        //   return unique.sort((a, b) => parseInt(a.Utc) - parseInt(b.Utc));
        // });
      }
    } catch (err) {
      console.error('Error fetching car data:', err);
    }
  };

  // 添加数据展示的定时器
  useEffect(() => {
    if (type === '20' && params.racingNumber) {
      // 获取数据的定时器 - 每0.5秒
      const fetchInterval = setInterval(() => {
        fetchLiveTimingData();
      }, 500);

      // 展示数据的定时器 - 每0.1秒
      const displayInterval = setInterval(() => {
        setCachedEntries(prevEntries => {
          if (prevEntries.length > 0) {
            setCurrentCarData(prevEntries[0].Channels);
            // 移除已显示的数据
            return prevEntries.slice(1);
          }
          return prevEntries;
        });
      }, 100);
      
      return () => {
        clearInterval(fetchInterval);
        clearInterval(displayInterval);
      };
    }
  }, [type, params.racingNumber]);

  useFocusEffect(
    useCallback(() => {
      return () => {
        if (reTimer) {
          clearInterval(reTimer);
        }
        player.destroy();
      };
    }, [player])
  );

  const onReady = useCallback((event: Event) => {
    prettyPrint(`EVENT [${event.name}]`, event);
    setSourceIsLoading(false);
  }, []);

  const onEvent = useCallback((event: Event) => {
    prettyPrint(`EVENT [${event.name}]`, event);
  }, []);

  const [currentCarData, setCurrentCarData] = useState<Channel | null>(null);
  
  return (
    <View style={styles.container}>
      <Stack.Screen
        options={{
          title: '',
          headerTintColor: '#fff',
          headerStyle: { backgroundColor: '#202020' },
          headerShown: !fullscreenMode,
          // headerShown: false,
          headerRight:  ()=>  <View style={{ display: 'flex', flexDirection:'row' }}>{type == '0' && streamsList.length > 0 && <Ionicons onPress={()=> setOpen(true)} name="reorder-three" size={35} color="white" />} 
          <Ionicons onPress={()=> setPlayerScalingMode(playerScalingMode == ScalingMode.Fit ? ScalingMode.Zoom : ScalingMode.Fit)} name="expand-outline" size={32} color="white" />
          <Ionicons onPress={()=> {
            offlineContentManager
            ?.download(downloadRequest)
            .catch(console.error);
          setDownloadRequest(initialDownloadRequest);
          }} name="reorder-three" size={35} color="white" />
          </View>,
        }}
      />
      {currentCarData && <TelemetryDashboard carData={currentCarData} />}
      <PlayerView
        player={player}
        style={fullscreenMode ? styles.playerFullscreen : styles.player}
        scalingMode={playerScalingMode}
        fullscreenHandler={fullscreenHandler}
        onReady={onReady}
        // onFullscreenEnter={onEvent}
        // onFullscreenExit={onEvent}
        // onFullscreenEnabled={onEvent}
        // onFullscreenDisabled={onEvent}
        // onPlay={onEvent}
        // onPlaying={onEvent}
        // onPaused={onEvent}
        // onSourceLoaded={onEvent}
        // onSeek={onEvent}
        // onEvent={onEvent}
        // onPlayerError={onEvent}
        // onSourceError={onEvent}
        // onSeeked={onEvent}
        // onStallStarted={onEvent}
        // onStallEnded={onEvent}
        // onVideoPlaybackQualityChanged={onEvent}
        // onCastAvailable={onEvent}
        // onCastPaused={onEvent}
        // onCastPlaybackFinished={onEvent}
        // onCastPlaying={onEvent}
        // onCastStarted={onEvent}
        // onCastStart={onEvent}
        // onCastStopped={onEvent}
        // onCastTimeUpdated={onEvent}
        // onCastWaitingForDevice={onEvent}
      />
      { sourceIsLoading && !fullscreenMode && <Spinner style={{ flex: 1, position: 'absolute', }} size="large" color="$blue11" />}
      
       <Sheet
        disableDrag={true}
        modal={true}
        open={open}
        onOpenChange={setOpen}
        snapPoints={[50]}
        snapPointsMode={"percent"}

        // dismissOnSnapToBottom
        zIndex={100_000}
        animation="medium"
      >
        <Sheet.Overlay
          animation="lazy"
          enterStyle={{ opacity: 0 }}
          exitStyle={{ opacity: 0 }}
        />
        <Sheet.Handle />
        <Sheet.Frame  justifyContent="center" alignItems="center">
          { streamsList.length > 0 &&renderStreamsSheet()}
        </Sheet.Frame>
      </Sheet>
      <Sheet
        disableDrag={true}
        modal={true}
        open={tokenChannelSheet}
        onOpenChange={setTokenChannelSheetOpen}
        snapPoints={[50]}
        snapPointsMode={"percent"}

        // dismissOnSnapToBottom
        zIndex={100_000}
        animation="medium"
      >
        <Sheet.Overlay
          animation="lazy"
          enterStyle={{ opacity: 0 }}
          exitStyle={{ opacity: 0 }}
        />
        <Sheet.Handle />
        <Sheet.Frame  justifyContent="center" alignItems="center">
          { tokenChannelList.length > 0 &&renderTokenChannelSheet()}
        </Sheet.Frame>
      </Sheet>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'black',
  },
  player: {
    flex: 1,
    backgroundColor: 'black',
  },
  playerFullscreen: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    backgroundColor: 'black',
  },
  dashboardContainer: {
    position: 'absolute',
    top: 10,
    left: 10,
    backgroundColor: 'rgba(0,0,0,0.7)',
    padding: 10,
    borderRadius: 10,
    zIndex: 1000,
  },
});
