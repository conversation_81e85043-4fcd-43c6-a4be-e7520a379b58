import { View,StyleSheet, FlatList, Platform } from "react-native";
import { useRouter,Stack, useLocalSearchParams } from 'expo-router';
import React, { useCallback, useRef, useEffect,  useState } from 'react';
import { Paragraph, SizableText, Text, Button, Spinner, Image, Card, useTheme } from 'tamagui'
import {
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import Ionicons from '@expo/vector-icons/Ionicons';
import axios from 'axios';
import { Sheet } from '@tamagui/sheet'
import { Input } from 'tamagui';
import AsyncStorage from '@react-native-async-storage/async-storage';
import dayjs from 'dayjs';
import DateTimePicker, { DateTimePickerAndroid } from '@react-native-community/datetimepicker';

export default function StarplusSeries() {
  const router = useRouter();
  const theme = useTheme()
  const insets = useSafeAreaInsets();
  const background = theme.background.get()
  const [date, setDate] = useState(new Date());
  const params = useLocalSearchParams();
  const { id = 0, } = params;
  const [open, setOpen] = useState(false)
  const [modaListData, setModalData] = useState<Array<starplusEpisodesEvent>>([]);
  const [listData, setListData] = useState<Array<starplusEvent>>([]);

  type starplusSeriesEvent = {
    seasonSequenceNumber?: string;
    seasonId?: string;
    imageUrl?: string;
  };

  type starplusEpisodesEvent = {
    episodeSeriesSequenceNumber?: string;
    name?: string;
    secondaryTitle?: string;
    imageUrl?: string;
    playbackUrl?: string;
  };


  useEffect(() => {
    renderCard(id);
  }, []);


  async function renderCard(id) {
    const jsonValue = await AsyncStorage.getItem('user_token');
    const json = jsonValue != null ? JSON.parse(jsonValue) : null;
    const dataSource: Array<starplusEvent> = [];
    axios({
      method: 'post',
      url: '/api/token',
      data: { type: '5' },
      headers: {
        Authorization: 'Bearer ' + json.access_token,
      },
    }).then((response0) => {
      const token = response0.data['token']; 
      axios.get(`https://star.content.edge.bamgrid.com/svc/content/DmcSeriesBundle/version/5.1/region/AR/audience/k-false,l-true/maturity/1850/language/en/encodedSeriesId/${id}`,
      {headers: {
        'authorization': token, 
        'x-dss-feature-filtering': 'true',
        'x-dss-edge-accept': 'vnd.dss.edge+json; version=1',
        'x-bamsdk-version': '9.4.1',
        'x-bamsdk-platform': 'android/google/handset',
        'x-application-version': '2.23.0-rc3.0',
        'x-bamsdk-client-id': 'star-22bcaf0a',
        'user-agent': 'BAMSDK/v9.4.1 (star-22bcaf0a 2.23.0-rc3.0; v5.0/v9.4.0; android; phone) OnePlus PJE110 (PJE110-user 13 N2G47H eng.root.20191015.144423 release-keys; Linux; 13; API 33)'
      }}
    )
      .then((response) => {
        const url = response.data.data.DmcSeriesBundle.series.image.tile["1.78"].series.default.url;
        const array: Array<Record<string, any>> = response.data.data.DmcSeriesBundle.seasons.seasons;
        if (array) {
          array.map((e) => {
            const a: starplusSeriesEvent = {};
            a.seasonId = e.seasonId;
            a.seasonSequenceNumber = e.seasonSequenceNumber;
            a.imageUrl = url;
            dataSource.push(a);
          });
        }
        setListData(dataSource);
      })
    
    })
  }

  function renderModalCard(id) {
    setModalData([]);
    setOpen(true);
    const dataSource: Array<starplusEpisodesEvent> = [];
    axios.get(`https://star.content.edge.bamgrid.com/svc/content/DmcEpisodes/version/5.1/region/AR/audience/k-false,l-true/maturity/1850/language/en/seasonId/${id}/pageSize/50/page/1`,
      {headers: {
        'X-Dss-Feature-Filtering': 'true',
        'X-Dss-Edge-Accept': 'vnd.dss.edge+json; version=1',
        'X-Bamsdk-Version': '27.1',
        'X-Bamsdk-Platform': 'javascript/macosx/chrome',
        'X-Application-Version': '1.0.0',
        'X-Bamsdk-Client-Id': 'star-22bcaf0a'
      }}
    )
      .then((response) => {
        const array: Array<Record<string, any>> = response.data.data.DmcEpisodes.videos;
        if (array) {
          array.map((e) => {
            const a: starplusEpisodesEvent = {};
            a.episodeSeriesSequenceNumber = e.episodeSeriesSequenceNumber;
            a.name = e.text.title.full.program.default.content;
            a.imageUrl = e.image.thumbnail["1.78"].program.default.url;
            a.secondaryTitle = e.text.description.brief.program.default.content;
            a.playbackUrl = e.mediaMetadata.playbackUrls[0].href
            dataSource.push(a);
          });
        }
        setModalData(dataSource);
      })
  }


  function formatDate(sessionStartDate: string) {
    const s = dayjs(sessionStartDate).format('MM-DD HH:mm');
    return s;
  }

  function pushToPlayer(id:string) {
    console.log(id)
    setOpen(false);
    router.push({pathname:'/pages/player', params:{id: encodeURIComponent(id), type: 6}})
  }

  function renderShowsSheet() {
    return <FlatList
      data={modaListData}
      numColumns={2}
      renderItem={({item}) => 
      <View style={ { marginRight: '2%', height: 150, width:'48%'}}>
        <Card  style={{ height: 144, overflow: "hidden"}} onPress={() =>  pushToPlayer(item.playbackUrl!)}>
          <Card.Footer style={{display: 'flex', flexDirection:'column'}}>
            <Text fontFamily="$body"  style={ {marginLeft:4,}}>{`${item.episodeSeriesSequenceNumber}. ${item.name}`}</Text>
            <Text fontFamily="$body" fontSize={10}  style={ {marginLeft:4}}>{item.secondaryTitle!}</Text>
          </Card.Footer>
          <Card.Background>
            <Image
              resizeMode="contain"
              alignSelf="center"
              source={{
                width: 200,
                height: 110,
                uri: item.imageUrl + '/scale?width=800&aspectRatio=1.78&format=jpeg'
              }}
            />
          </Card.Background>
        </Card>
      </View>}
    />
  }

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingTop: 22,
    },
    item: {
      padding: 5,
      fontSize: 18,
    },
  });
  
  return (
    <View style={{ flex: 1, alignItems: "center",  }}>
      <Stack.Screen
        options={{
          title: '',
          headerTitle:()=> <Text fontFamily="$body" fontSize={26}>Star+ Series</Text>,
          headerTintColor: '#fff',
          headerStyle: { backgroundColor: '#202020',  },
        }}
      />
      { listData.length == 0 && <Spinner size="large" color="$blue11" />}
      { listData.length > 0 && <FlatList
        
        style={{ backgroundColor: background, marginLeft: '2%'}}
        data={listData}
        numColumns={2}
        renderItem={({item}) => 
        <View style={ {marginBottom: 10,  marginRight: '2%', width: '48%'}}>
          <Card  style={{height: 134, overflow: "hidden" }} onPress={() => {
            // encodedFamilyId(item.encodedFamilyId!);
            renderModalCard(item.seasonId);
          }}>
            <Card.Header padded></Card.Header>
            <Card.Footer style={{display: 'flex', flexDirection:'column'}} >
              <Text fontFamily="$body" numberOfLines={1} style={ { marginLeft:4, }}>{`Season ${item.seasonSequenceNumber}`}</Text>
            </Card.Footer>
            <Card.Background>
              <Image
                resizeMode="contain"
                alignSelf="center"
                source={{
                  width: 200,
                  height: 110,
                  uri: item.imageUrl + '/scale?width=800&aspectRatio=1.78&format=jpeg'
                }}
              />
            </Card.Background>
          </Card>
        </View>}
      />}
      <Sheet
        disableDrag={true}
        modal={true}
        open={open}
        onOpenChange={setOpen}
        snapPoints={[80]}
        snapPointsMode={"percent"}

        // dismissOnSnapToBottom
        zIndex={100_000}
        animation="medium"
      >
        <Sheet.Overlay
          animation="lazy"
          enterStyle={{ opacity: 0 }}
          exitStyle={{ opacity: 0 }}
        />
        <Sheet.Handle />
        <Sheet.Frame  justifyContent="center" alignItems="center">
          { modaListData.length == 0 && <Spinner size="large" color="$blue11" />}
          { modaListData.length > 0 &&renderShowsSheet()}
        </Sheet.Frame>
      </Sheet>
    </View>
    
  );
}


