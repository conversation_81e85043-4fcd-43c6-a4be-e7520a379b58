import { View,StyleSheet, FlatList, Image } from "react-native";
import { useRouter,Stack, useLocalSearchParams } from 'expo-router';
import React, { useCallback, useRef, useEffect,  useState } from 'react';
import {  Text, Spinner, Card, useTheme } from 'tamagui'
import {
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { Sheet } from '@tamagui/sheet'
import Ionicons from '@expo/vector-icons/Ionicons';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import dayjs from 'dayjs';

export default function Season() {
  const router = useRouter();
  const theme = useTheme()
  const insets = useSafeAreaInsets();
  const background = theme.background.get()

  const [open, setOpen] = useState(false)
  const [modaListData, setModalData] = useState<Array<event>>([]);
  const params = useLocalSearchParams();
  const { id = 0, title = ''} = params;



  const [listData, setData] = useState<Array<session>>([]);

  type session = {
    id?: string;
    Global_Title?: string;
    Global_Meeting_Name?: string;
    Global_Meeting_Country_Name?: string;
    Meeting_Display_Date?: string;
    pictureUrl?: string;
    pid?: string;
    season?: number;
    Meeting_Number?: string;
    meeting_Start_Date?: number;
    meeting_End_Date?: number;
  };

  type event = {
    id?: string;
    title?: string;
    pictureUrl?: string;
    pid?: string;
    series?: string;
    year?: string;
    Meeting_Number?: string;
    longDescription?: string;
  };

  useEffect(() => {
    renderCard(params.id);
  }, []);


  async function renderCard(id: any) {
    const jsonValue = await AsyncStorage.getItem('user_token');
    const json = jsonValue != null ? JSON.parse(jsonValue) : null;
    axios({
      method: 'post',
      url: '/api/session',
      data: { session: id },
      headers: {
        Authorization: 'Bearer ' + json.access_token,
      },
    })
      .then((response) => {
        const dataSource: Array<session> = [];
        const json = JSON.parse(response['data']['json']);
        const array: Array<Record<string, any>> = json;
        array.map((s) => {
          const a: session = {};
          a.id = s.id.toString();
          a.pictureUrl = s.metadata['pictureUrl'].toString();
          a.Global_Title = s.metadata['title'].toString();
          a.Global_Meeting_Name =
            s.metadata['emfAttributes']['Global_Meeting_Name'].toString();
          a.Global_Meeting_Country_Name =
            s.metadata['emfAttributes'][
              'Global_Meeting_Country_Name'
            ].toString();
          a.Meeting_Display_Date =
            s.metadata['emfAttributes']['Meeting_Display_Date'].toString();
          a.pid = s.metadata['emfAttributes']['PageID'];
          a.season = parseInt(s.metadata['season']);
          a.Meeting_Number =
            s.metadata['emfAttributes']['Meeting_Number'].toString();
          if (s['properties']) {
            a.meeting_Start_Date = s['properties'][0]['meeting_Start_Date'];
            a.meeting_End_Date = s['properties'][0]['meeting_End_Date'];
          } else {
            const sdate = new Date(
              s.metadata['emfAttributes']['Meeting_Start_Date']
            );
            const edate = new Date(
              s.metadata['emfAttributes']['Meeting_End_Date']
            );

            a.meeting_Start_Date = sdate.getTime();
            a.meeting_End_Date = edate.getTime();
          }

          dataSource.push(a);
        });
        setData(dataSource);
      })
  }

  function pushToPlayer(id:string) {
    setOpen(false);
    router.push({pathname:'/pages/player', params:{id: id}})
  }

  async function renderEventCard(pid:String, cid: string) {
    if (pid) {
      setModalData([]);
    setOpen(true);
    const jsonValue = await AsyncStorage.getItem('user_token');
    const json = jsonValue != null ? JSON.parse(jsonValue) : null;
    axios({
      method: 'post',
      url: '/api/weekend',
      data: { id: pid },
      headers: {
        Authorization: 'Bearer ' + json.access_token,
      },
    })
      .then((response) => {
        const array: Array<Record<string, any>> = response.data.containers;
        let replays: Array<Record<string, any>> = [];
        const labels = [
          'Pre-Season Testing Sessions',
          'Replays',
          'Live & Replays',
          'Session Replays',
          'Weekend Sessions',
        ];

        array.map((item) => {
          const label: string = item.metadata['label'];
          if (item.layout == 'horizontal_thumbnail' && labels.includes(label)) {
            replays = item.retrieveItems['resultObj']['containers'];
          }
        });
        const dataSource: Array<event> = [];
        replays.map((e) => {
          const a: event = {};
          a.id = e.id.toString();
          a.pictureUrl = e.metadata['pictureUrl'].toString();
          a.title = e.metadata['titleBrief'].toString();
          a.series = e.properties[0].series.toString();
          a.Meeting_Number =
            e.metadata['emfAttributes']['Meeting_Number'].toString();
          a.longDescription =
            e.metadata['longDescription'].toString().toLowerCase() == 'race'
              ? 'results'
              : e.metadata['longDescription'].toString().toLowerCase();
          a.year = e.metadata['year'].toString();
          dataSource.push(a);
          // }
        });
        setModalData(dataSource);
      });
      return
    }
    if (cid) {
      pushToPlayer(cid)
    }
    
  }

  function renderShowsSheet() {
    return <FlatList
      data={modaListData}
      numColumns={2}
      renderItem={({item}) => 
      <View style={ { marginRight: '2%', height: 150, width:'48%'}}>
        <Card  style={{ height: 144, overflow: "hidden"}} onPress={() =>  pushToPlayer(item.id!)}>
          <Card.Footer style={{display: 'flex', flexDirection:'column'}}>
            <Text fontFamily="$body"  style={ {marginLeft:4,}}>{item.title!}</Text>
            <Text fontFamily="$body" fontSize={10}  style={ {marginLeft:4}}>{item.series!}</Text>
          </Card.Footer>
          <Card.Background>
            <Image
              resizeMode="center"
              style={{width: '100%',height:110,}}
              source={{
                uri: 'https://f1tv.formula1.com/image-resizer/image/' +
                item.pictureUrl +
                '?w=700&h=400&q=HI&o=L'
              }}
            />
          </Card.Background>
        </Card>
      </View>}
    />
  }


  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingTop: 22,
    },
    item: {
      padding: 5,
      fontSize: 18,
    },
  });
  
  return (
    <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
      <Stack.Screen
        
        options={{
          title: '',
          headerTitle:()=> <Text fontFamily="$body" fontSize={26}>{params.title}</Text>,
          headerTintColor: '#fff',
          headerStyle: { backgroundColor: '#202020',  },
          headerBackTitle: 'Home'
        }}
      />
      { listData.length == 0 && <Spinner size="large" color="$blue11" />}
      { listData.length > 0 && <FlatList
        
        style={{ backgroundColor: background, marginLeft: '2%',}}
        data={listData}
        numColumns={2}
        renderItem={({item}) => 
        <View style={ {marginBottom: 10,  marginRight: '2%', width: '48%'}}>
          <Card  style={{ height: 164, overflow: "hidden"}} onPress={() =>  {renderEventCard(item.pid!, item.id!)}}>
                <Card.Header padded></Card.Header>
                <Card.Footer style={{display: 'flex', flexDirection:'column'}}>
                  <Text fontFamily="$body" style={ {marginLeft:4,}}>{item.Global_Meeting_Name!}</Text>
                  {item.Meeting_Display_Date && <Text fontFamily="$body" fontSize={11} style={ {marginLeft:4,}}>{item.Meeting_Display_Date!}</Text>}
                  <Text fontFamily="$body" numberOfLines={1} fontSize={11} style={ {marginLeft:4,}}>{item.Global_Title!}</Text>
                </Card.Footer>
                <Card.Background>
                  <Image
                    resizeMode="center"
                    style={{width: '100%',height:110, opacity: dayjs(item.meeting_Start_Date!).valueOf() < Date.now() ? 1 : 0.5 }}
                    source={{
                      uri: 'https://f1tv.formula1.com/image-resizer/image/' +
                      item.pictureUrl +
                      '?w=700&h=400&q=HI&o=L'
                    }}
                  />
                </Card.Background>
              </Card>
          
        </View>}
      />}
      <Sheet
        disableDrag={true}
        modal={true}
        open={open}
        onOpenChange={setOpen}
        snapPoints={[80]}
        snapPointsMode={"percent"}

        // dismissOnSnapToBottom
        zIndex={100_000}
        animation="medium"
      >
        <Sheet.Overlay
          animation="lazy"
          enterStyle={{ opacity: 0 }}
          exitStyle={{ opacity: 0 }}
        />
        <Sheet.Handle />
        <Sheet.Frame  justifyContent="center" alignItems="center">
          { modaListData.length == 0 && <Spinner size="large" color="$blue11" />}
          { modaListData.length > 0 &&renderShowsSheet()}
        </Sheet.Frame>
      </Sheet>
    </View>
    
  );
}

