import { <PERSON>, Platform, ScrollView } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Paragraph, SizableText, Text, Button, Spinner, Image, Card, useTheme, XStack, YStack } from 'tamagui';
import { useState, useEffect } from 'react';
import axios from 'axios';
import dayjs from 'dayjs';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface F1Data {
  f1LiveTimingClock: {
    paused: boolean;
    systemTime: string;
    trackTime: string;
    liveTimingStartTime: string;
  };
  version: string;  
  players: {
    id: string;
    state: {
      currentTime: number;
      interpolatedCurrentTime: number;
    };
    type: string;
    driverData: {
      driverNumber: string;
      tla: string;
      firstName: string;
      lastName: string;
      teamName: string;
    };
    streamData: {
      title: string;
      channelId: string;
      contentId: string;
    };
      
  }[];
  f1LiveTimingState: {
    SessionInfo: {
      Meeting: {
        Name: string;
        OfficialName: string;
        Location: string;
        Country: {
          Name: string;
        };
        Circuit: {
          ShortName: string;
        };
      };
    };
  };
}



// 修改时间转换函数
const formatDateTime = (timestamp: string | number) => {
  try {
    const timestampNum = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;
    return dayjs(timestampNum).format('YYYY-MM-DD HH:mm:ss');
  } catch (error) {
    console.error('Time conversion error:', error, timestamp);
    return timestamp;
  }
};

export default function MvScreen() {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const background = theme.background.get();
  const router = useRouter();
  const [data, setData] = useState<F1Data | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      const response = await axios.post('http://192.168.124.27:10101/api/graphql', {
        query: `
          query ExampleQuery {
            f1LiveTimingClock {
              paused
              systemTime
              trackTime
              liveTimingStartTime
            }
            version
            players {
              id
              state {
                currentTime
                interpolatedCurrentTime
              }
              type
              driverData {
                driverNumber
                tla
                firstName
                lastName
                teamName
              }
              streamData {
                contentId
                channelId
                title
              }
            }
            f1LiveTimingState {
              SessionInfo
            }
          }
        `
      });
      
      console.log('Response:', response.data);
      
      if (response.data && response.data.data) {
        setData(response.data.data);
        setError(null);
      } else {
        setError('数据格式错误');
      }
      
      setLoading(false);
    } catch (err) {
      console.error('Error:', err);
      setError(err instanceof Error ? err.message : '发生错误');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  function pushToPlayer(id:string, racingNumber:string) {
    
    router.push({pathname:'/pages/player', params:{id: id, type: '20', racingNumber: racingNumber}})
  }

  return (
    <View style={{ flex: 1, backgroundColor: background }}>
      <Stack.Screen
        options={{
          title: '',
          headerTitle: () => <Text fontFamily="$body" fontSize={26}>Mv</Text>,
          headerTintColor: '#fff',
          headerStyle: { backgroundColor: '#202020' },
          headerRight: () => (
            <Button
              size="$3"
              onPress={fetchData}
              marginRight="$2"
              backgroundColor="$blue10"
            >
              刷新
            </Button>
          ),
        }}
      />
      <ScrollView style={{ flex: 1 }} contentContainerStyle={{ padding: 10 }}>
        <Text fontSize={12} color="$gray10" marginBottom="$2">MultiViewer版本: {data?.version || '--'}</Text>
        {loading ? (
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
            <Spinner size="large" />
          </View>
        ) : error ? (
          <View style={{ justifyContent: 'center', alignItems: 'center', padding: 20 }}>
            <Text color="$red10">{error}</Text>
          </View>
        ) : data && (data.f1LiveTimingClock || data.players) ? (
          <View>
            
            {data.f1LiveTimingClock ? <Card elevate size="$4" bordered style={{ marginBottom: 10 }}>
              <Card.Header>
                <Text fontSize={16} fontWeight="bold">Livetiming</Text>
                <YStack space="$1" style={{paddingTop:10}}>
                  {/* <Text fontSize={14}>比赛: {data.f1LiveTimingState.SessionInfo.Meeting.Name}</Text> */}
                  <Text fontSize={14}>{data.f1LiveTimingState.SessionInfo.Meeting.OfficialName}</Text>
                  <Text fontSize={14}>赛道时间: {formatDateTime(data.f1LiveTimingClock.trackTime)}</Text>
                  <Text fontSize={14}>开始时间: {formatDateTime(data.f1LiveTimingClock.liveTimingStartTime)}</Text>
                  <Text fontSize={14}>当前时间: {formatDateTime(data.f1LiveTimingClock.systemTime)}</Text>
                </YStack>
              </Card.Header>
            </Card> : <View style={{ justifyContent: 'center', alignItems: 'center', padding: 20 }}>
                <Text>暂无livetiming数据</Text>
              </View>}

            {data.players ? (
              <XStack flexWrap="wrap" justifyContent="space-between">
                {data.players.map((player) => (
                  <Card 
                    key={player.id} 
                    elevate 
                    size="$4" 
                    bordered 
                    style={{ 
                      width: '48%',
                      marginBottom: 10 
                    }}
                    onPress={() => pushToPlayer(`CONTENT/PLAY?channelId=${player.streamData.channelId}&contentId=${player.streamData.contentId}`, player.driverData.driverNumber)}
                  >
                    <Card.Header padded>
                      <Text fontSize={16} fontWeight="bold" numberOfLines={1}>
                        {player.streamData.title}
                      </Text>
                      <YStack space="$2" style={{paddingTop:10}}>
                        {player.type === 'OBC' ? (
                          <View>
                            <Text numberOfLines={1}>车队: {player.driverData.teamName}</Text>
                            <Text>车号: {player.driverData.driverNumber}</Text>
                            {/* <Text>当前时间: {player.state.interpolatedCurrentTime}</Text> */}
                          </View>
                        ) : (
                          <Text>类型: {player.type}</Text>
                        )}
                      </YStack>
                    </Card.Header>
                  </Card>
                ))}
              </XStack>
            ) : (
              <View style={{ justifyContent: 'center', alignItems: 'center', padding: 20 }}>
                <Text>暂无播放器数据</Text>
              </View>
            )}
          </View>
        ) : (
          <View style={{ justifyContent: 'center', alignItems: 'center', padding: 20 }}>
            <Text>暂无数据</Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
} 