import { View,StyleSheet, FlatList, Platform } from "react-native";
import { useRouter,Stack, useLocalSearchParams } from 'expo-router';
import React, { useCallback, useRef, useEffect,  useState } from 'react';
import { Paragraph, SizableText, Text, Button, Spinner, Image, Card, useTheme } from 'tamagui'
import {
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import Ionicons from '@expo/vector-icons/Ionicons';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import dayjs from 'dayjs';
import DateTimePicker, { DateTimePickerAndroid } from '@react-native-community/datetimepicker';

export default function Viaplay() {
  const router = useRouter();
  const theme = useTheme()
  const insets = useSafeAreaInsets();
  const background = theme.background.get()
  const [date, setDate] = useState(new Date());
  const params = useLocalSearchParams();
  const { id = 0, } = params;


  const [listData, setListData] = useState<Array<viaplayEvent>>([]);

  type viaplayEvent = {
    productKey?: string;
    title?: string;
    formatTitle?: string;
    originalTitle?: string;
    pictureUrl?: string;
    logoUrl?: string;
    streamStart?: string;
    streamEnd?: string;
    commentators?: string;
  };

  useEffect(() => {
    renderCard(dayjs(date).format('YYYY-MM-DD'));
  }, []);

  const onChange = (event: any, selectedDate: any) => {
    setDate(selectedDate);
    if (Platform.OS == 'android') {
      console.log(selectedDate)
      setListData([]);
      renderCard(dayjs(selectedDate).format('YYYY-MM-DD'));
    } else {
      if (event['type'] == 'dismissed' ) {
        // const currentDate = selectedDate;
        setListData([]);
        renderCard(dayjs(date).format('YYYY-MM-DD'));
      }
    }
  };

  const showDateTimePickerAndroid = () => {
    DateTimePickerAndroid.open({
      value: date,
      maximumDate: new Date(),
      onChange,
      mode: 'date',
      is24Hour: true,
    });
  };


  async function renderCard(datestr: string) {
    const jsonValue = await AsyncStorage.getItem('user_token');
    const json = jsonValue != null ? JSON.parse(jsonValue) : null;
    const dataSource: Array<viaplayEvent> = [];
    axios({
      method: 'post',
      url: '/api/others_list',
      data: { date: datestr },
      headers: {
        Authorization: 'Bearer ' + json.access_token,
      },
    })
      .then((response) => {
        const rarray: Array<Record<string, any>> = response.data;
        rarray.map((ri) => {
          if (ri.title == 'Aikataulu' && ri['_embedded']) {

            const array: Array<Record<string, any>> =
              ri['_embedded']['viaplay:products'];
            if (array) {
              array.map((e) => {
                const ea: Array<Record<string, any>> =
                  e['_links']['viaplay:genres'];
                if (
                  e.recording &&
                  e.recording['airingType'] == 'live'
                ) {
                  const a: viaplayEvent = {};
                  a.productKey = e.system['productKey'].toString();
                  let logourl = '';
                  if (e.content['images']['logoCentered']) {
                    logourl = e.content['images']['logoCentered']['template']
                      .toString()
                      .split('{')[0];
                  } else if (e.content['images']['logo']) {
                    logourl = e.content['images']['logo']['template']
                      .toString()
                      .split('{')[0];
                  }
                  a.logoUrl = logourl;
                  a.pictureUrl =
                    e.content['images']['landscape']['url'].toString();
                  a.title = e.content['title'];
                  a.formatTitle = e.content['format']['title'].toString();
                  a.originalTitle = e.content['originalTitle']
                    ? e.content['originalTitle'].toString()
                    : '';
                  a.streamStart = e.epg['streamStart'].toString();
                  a.streamEnd = e.epg['streamEnd'].toString();
                  a.commentators =
                    e.content['description']['commentators'][
                      'value'
                    ].toString();
                  dataSource.push(a);
                }
              });
            }
          }
        });
        dataSource.sort(function (x, y) {
          return dayjs(x.streamStart) < dayjs(y.streamStart) ? -1 : 1;
        });
        setListData(dataSource);
      })
  }

  function formatDate(sessionStartDate: string) {
    const s = dayjs(sessionStartDate).format('MM-DD HH:mm');
    return s;
  }

  function pushToPlayer(id:string, title: string) {
    console.log(id)
    router.push({pathname:'/pages/player', params:{id: id, type: 4}})
  }


  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingTop: 22,
    },
    item: {
      padding: 5,
      fontSize: 18,
    },
  });
  
  return (
    <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
      <Stack.Screen
        options={{
          title: '',
          headerTitle:()=> <Text fontFamily="$body" fontSize={26}>Viaplay</Text>,
          headerTintColor: '#fff',
          headerStyle: { backgroundColor: '#202020',  },

          headerRight:  ()=> Platform.OS == 'ios' ?  <DateTimePicker
              testID="dateTimePicker"
              maximumDate={new Date()}
              value={date}
              mode='date'
              onChange={onChange}
            /> : <Button onPress={showDateTimePickerAndroid}>{dayjs(date).format('MM-DD')}</Button> ,
        }}
      />
  
      { listData.length == 0 && <Spinner size="large" color="$blue11" />}
      { listData.length > 0 && <FlatList
        
        style={{ backgroundColor: background, marginLeft: '2%'}}
        data={listData}
        numColumns={2}
        renderItem={({item}) => 
        <View style={ {marginBottom: 10,  marginRight: '2%', width: '48%'}}>
          <Card  style={{height: 184, overflow: "hidden" }} onPress={() => dayjs(item.streamStart!).valueOf() < Date.now() ? pushToPlayer(item.productKey!, item.title!) : {}}>
            <Card.Header padded></Card.Header>
            <Card.Footer style={{display: 'flex', flexDirection:'column'}} >
              <Text fontFamily="$body" numberOfLines={1} style={ { marginLeft:4, }}>{item.title!}</Text>
              <Text fontFamily="$body" numberOfLines={1} style={ { marginLeft:4, }}>{ `${item.formatTitle!} | ${item.originalTitle!}`}</Text>
              <Text fontFamily="$body" style={ { marginLeft:4, }}>{formatDate(item.streamStart!)}</Text>
            </Card.Footer>
            <Card.Background>
              <Image
                resizeMode="contain"
                alignSelf="center"
                opacity={dayjs(item.streamStart!).valueOf() < Date.now() ? 1 : 0.5 }
                source={{
                  width: 200,
                  height: 110,
                  uri: item.pictureUrl
                }}
              />
            </Card.Background>
          </Card>
        </View>}
      />}
    </View>
    
  );
}

