import { View,StyleSheet, FlatList, Platform } from "react-native";
import { useRouter,Stack, useLocalSearchParams } from 'expo-router';
import React, { useCallback, useRef, useEffect,  useState } from 'react';
import { Paragraph, SizableText, Text, Button, Spinner, Image, Card, useTheme } from 'tamagui'
import {
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import Ionicons from '@expo/vector-icons/Ionicons';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import dayjs from 'dayjs';
import DateTimePicker, { DateTimePickerAndroid } from '@react-native-community/datetimepicker';

export default function Eurosport() {
  const router = useRouter();
  const theme = useTheme()
  const background = theme.background.get()
  const params = useLocalSearchParams();

  const hamiListData = [
    {
      logo: 'https://assets.livednow.com/logo/ELTA體育-1台.png',
      name: '體育1台',
      id: 'OTT_LIVE_0000001744'
    },
    {
      logo: 'https://assets.livednow.com/logo/ELTA體育-2台.png',
      name: '體育2台',
      id: 'OTT_LIVE_0000001743'
    },
    {
      logo: 'https://assets.livednow.com/logo/ELTA體育-3台.png',
      name: '體育3台',
      id: 'OTT_LIVE_0000001745'
    },
    {
      logo: 'https://assets.livednow.com/logo/ELTA體育-4台.png',
      name: '體育4台',
      id: 'OTT_LIVE_0000002055'
    },
    {
      logo: 'https://assets.livednow.com/logo/ELTA體育-MAX1.png',
      name: '體育MAX1台',
      id: 'OTT_LIVE_0000001853'
    },
    {
      logo: 'https://assets.livednow.com/logo/ELTA體育-MAX2.png',
      name: '體育MAX2台',
      id: 'OTT_LIVE_0000001854'
    },
    {
      logo: 'https://assets.livednow.com/logo/ELTA體育-MAX3.png',
      name: '體育MAX3台',
      id: 'OTT_LIVE_0000001855'
    },
    {
      logo: 'https://assets.livednow.com/logo/ELTA體育-MAX4.png',
      name: '體育MAX4台',
      id: 'OTT_LIVE_0000001856'
    },
    {
      logo: 'https://assets.livednow.com/logo/hami-mlb.png',
      name: 'Hami MLB 台',
      id: 'OTT_LIVE_0000002017'
    },
    {
      logo: 'https://assets.livednow.com/logo/hami-yq.png',
      name: 'Hami 羽球台',
      id: 'OTT_LIVE_0000001830'
    },
    {
      logo: 'https://assets.livednow.com/logo/博斯運動一台.png',
      name: '博斯運動一台',
      id: 'OTT_LIVE_0000001845'
    },
    {
      logo: 'https://assets.livednow.com/logo/博斯網球台.png',
      name: '博斯網球台',
      id: 'OTT_LIVE_0000001903'
    },
    {
      logo: 'https://assets.livednow.com/logo/博斯魅力台.png',
      name: '博斯魅力台',
      id: 'OTT_LIVE_0000001972'
    },
    {
      logo: 'https://assets.livednow.com/logo/博斯高球台.png',
      name: '博斯高球一台',
      id: 'OTT_LIVE_0000001846'
    },
    {
      logo: 'https://assets.livednow.com/logo/博斯無限台.png',
      name: '博斯無限台',
      id: 'OTT_LIVE_0000001882'
    },
    {
      logo: 'https://assets.livednow.com/logo/博斯無限二台.png',
      name: '博斯無限二台',
      id: 'OTT_LIVE_0000001973'
    },
  ]


  useEffect(() => {
  }, []);

  function pushToPlayer(id:string) {
    console.log(id)
    router.push({pathname:'/pages/player', params:{id: id, type: 13}})
  }


  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingTop: 22,
    },
    item: {
      padding: 5,
      fontSize: 18,
    },
  });
  
  return (
    <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
      <Stack.Screen
        options={{
          title: '',
          headerTitle:()=> <Text fontFamily="$body" fontSize={26}>Hami</Text>,
          headerTintColor: '#fff',
          headerStyle: { backgroundColor: '#202020',  },
          headerBackTitle: 'Home'
        }}
      />
  
      { hamiListData.length == 0 && <Spinner size="large" color="$blue11" />}
      { hamiListData.length > 0 && <FlatList
        
        style={{ backgroundColor: background, marginLeft: '2%'}}
        data={hamiListData}
        numColumns={2}
        renderItem={({item}) => 
        <View style={ {marginBottom: 10,  marginRight: '2%', width: '48%'}}>
          <Card  style={{height: 104, overflow: "hidden" }} onPress={() =>  pushToPlayer(item.id)}>
            <Card.Header padded></Card.Header>
            <Card.Footer style={{display: 'flex', flexDirection:'column'}} >
              <Text fontFamily="$body" numberOfLines={1} style={ { marginLeft:4, }}>{item.name!}</Text>
            </Card.Footer>
            <Card.Background>
              <Image
                resizeMode="contain"
                alignSelf="center"
                source={{
                  width: 100,
                  height: 110,
                  uri: item.logo
                }}
              />
            </Card.Background>
          </Card>
        </View>}
      />}
    </View>
    
  );
}

