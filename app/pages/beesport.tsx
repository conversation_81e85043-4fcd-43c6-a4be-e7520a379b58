import { View,StyleSheet, FlatList, Platform } from "react-native";
import { useRouter,Stack, useLocalSearchParams } from 'expo-router';
import React, { useCallback, useRef, useEffect,  useState } from 'react';
import { Paragraph, SizableText, Text, Button, Spinner, Image, Card, useTheme } from 'tamagui'
import {
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import Ionicons from '@expo/vector-icons/Ionicons';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import dayjs from 'dayjs';
import DateTimePicker, { DateTimePickerAndroid } from '@react-native-community/datetimepicker';

export default function Eurosport() {
  const router = useRouter();
  const theme = useTheme()
  const background = theme.background.get()
  const params = useLocalSearchParams();


  const [listData, setListData] = useState<Array<beeSportStream>>([]);

  type beeSportStream = {
    name?: string;
    logo?: string;
    url?: string;

  };

  useEffect(() => {
    renderCard();
  }, []);

  async function renderCard() {
    fetchBeesportList()
  }

  function fetchBeesportList() {
    axios.get(
      'https://x.histreams.net/beesport.m3u'
    )
    .then((response) => {
      const Rep0 = /tvg-name="(.*?)"|tvg-logo="(.*?)"|(https:.*?)(?=(\s|$))/g;
      const patt0 = new RegExp(Rep0);
      const result0 = response.data.match(patt0);
      const dataSource: Array<beeSportStream> = [];
      let t_name: string;
      let t_logo: string;
      result0.map((item: string) => {
        if (item.indexOf('tvg-name') != -1) {
          t_name = item.substring(10, item.length-1);
        }
        if (item.indexOf('tvg-logo') != -1) {
          t_logo = item.substring(10, item.length-1);
        }
        if (item.indexOf('histreams') != -1) {
          const urls = item.split('/beesport/');
          // const url = `https://x.histreams.net/beesport/${}`
          const i: beeSportStream = {
            name: t_name,
            logo: t_logo,
            url: urls[1]
          }
          dataSource.push(i);
        }
      })
      setListData(dataSource);
    })
}

  function formatDate(sessionStartDate: string) {
    const s = dayjs(sessionStartDate).format('MM-DD HH:mm');
    return s;
  }

  function pushToPlayer(id:string) {
    console.log(id)
    router.push({pathname:'/pages/player', params:{id: id, type: 12}})
  }


  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingTop: 22,
    },
    item: {
      padding: 5,
      fontSize: 18,
    },
  });
  
  return (
    <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
      <Stack.Screen
        options={{
          title: '',
          headerTitle:()=> <Text fontFamily="$body" fontSize={26}>Sports</Text>,
          headerTintColor: '#fff',
          headerStyle: { backgroundColor: '#202020',  },
          headerBackTitle: 'Home'
        }}
      />
  
      { listData.length == 0 && <Spinner size="large" color="$blue11" />}
      { listData.length > 0 && <FlatList
        
        style={{ backgroundColor: background, marginLeft: '2%'}}
        data={listData}
        numColumns={2}
        renderItem={({item}) => 
        <View style={ {marginBottom: 10,  marginRight: '2%', width: '48%'}}>
          <Card  style={{height: 104, overflow: "hidden" }} onPress={() =>  pushToPlayer(item.url!)}>
            <Card.Header padded></Card.Header>
            <Card.Footer style={{display: 'flex', flexDirection:'column'}} >
              <Text fontFamily="$body" numberOfLines={1} style={ { marginLeft:4, }}>{item.name!}</Text>
            </Card.Footer>
            <Card.Background>
              <Image
                resizeMode="contain"
                alignSelf="center"
                source={{
                  width: 100,
                  height: 110,
                  uri: item.logo
                }}
              />
            </Card.Background>
          </Card>
        </View>}
      />}
    </View>
    
  );
}

