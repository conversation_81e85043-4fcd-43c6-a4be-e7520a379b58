import { Stack , useRouter, usePathname} from "expo-router";
import { AuthProvider } from "../context/AuthProvider";
import { Toast, ToastProvider, useToastController, ToastViewport } from '@tamagui/toast'
import { TamaguiProvider } from 'tamagui'
import config from '../tamagui.config'
import { useFonts } from 'expo-font'
import { useEffect, useState,} from 'react';
import axios from 'axios';
import { StatusBar } from 'expo-status-bar';
import { useAuth } from "../context/AuthProvider";
import { Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function RootLayout() {

  const pathname = usePathname();
  const router = useRouter();
  const { setUser } = useAuth();
  const [currentPathName, setPathname] = useState('/');
  const [loaded] = useFonts({
    Inter: require("../assets/TitilliumWeb-Regular.ttf"),
    InterBold: require("../assets/TitilliumWeb-Bold.ttf"),
    // Titillium: require("../assets/TitilliumWeb-Regular.ttf"),
    // TitilliumBold: require("../assets/TitilliumWeb-Bold.ttf"),
    // Inter: require("@tamagui/font-inter/otf/Inter-Medium.otf"),
    // InterBold: require("@tamagui/font-inter/otf/Inter-Bold.otf"),
  });

  // 是否正在请求刷新token接口的标记
  let isRefreshing = false;
  // 请求队列
  let requests: any[] = [];
  // 创建一个axios实例
  const instance = axios.create({
    timeout: 300000,
  });
  //刷新token方法
  async function refreshToken(refresh_token: string, token: string) {
  
    return instance({ 
      method: 'post',
      url: axios.defaults.baseURL + "/api/login/refresh", 
      data: { refresh_token: refresh_token },
      headers: {
        Authorization: 'Bearer ' + token,
      }
    });
  }

  const showAlert = (msg:any, remove: boolean) =>
    Alert.alert(
      '',
        msg,
      [
        {
          text: 'close',
          onPress: () =>{ remove ? removeValue() : {}},
          style: 'destructive',
        },
      ],
      {
        cancelable: true
      },
    );

  const removeValue = async () => {
    try {
      await AsyncStorage.removeItem('user_token')
      console.log('setUser Done.')
      router.replace("/login")
    } catch(e) {
      // remove error
    }
  
    console.log('Done.')
  }

  useEffect(() => {
    console.log(pathname)
    setPathname(pathname)
    console.log(currentPathName)
  },[pathname])

  useEffect(() => {

    
    if (loaded) {
      // can hide splash screen here
      console.log('app useEffect loaded')
      AsyncStorage.getItem('api').then( e => {
        console.log(e)
        if (process.env.NODE_ENV === 'development') {
          // axios.defaults.baseURL = 'http://192.168.124.79:8000'

          // AsyncStorage.setItem('api', 'http://192.168.124.79:8000')
          if (e != null) {
            axios.defaults.baseURL = e
          } else {
            axios.defaults.baseURL = 'https://www.histreams.net'
            AsyncStorage.setItem('api', 'https://www.histreams.net')
          }
        } else {
          if (e != null) {
            axios.defaults.baseURL = e
          } else {
            axios.defaults.baseURL = 'https://www.histreams.net'
            AsyncStorage.setItem('api', 'https://www.histreams.net')
          }
        }
        
        
      })
      
      axios.interceptors.request.use(
        function (config) {
          const { url, baseURL } = config;
          console.log(url);
          console.log(baseURL);
          if (url != '/api/token') {
            return config;
          } else {
            return config;
          }
        },
        (error) => {
          return Promise.reject(error);
        }
      );
      axios.interceptors.response.use(
        (data:any) => {
          
          // console.log('response')
          showPathName()
          if (data.data && data.data['errorDescription'] && data.data['errorDescription'] != '200') {

            console.log(data.data)
            showAlert(JSON.stringify(data.data), false);
          }
          if (data.status != null && data.status == 200) {
            return data;
          }
          if (data.data.status && data.data.status !== 200) {
            // console.log(data);
            // showAlert('Error 451: Unavailable Due to Legal Reasons');
          }
          return data;
        },
        (err) => {
          
          // console.log(err.response);
          if (err.response && err.response.status == 451) {
            // console.log(err.response);
            showAlert('Error 451: Unavailable Due to Legal Reasons', false);
            return Promise.resolve(err);
          }
          if (err.response && err.response.status == 401) {
            if (err.response.config.url.includes('f1tv.formula1.com')) {
              showAlert(err.response.data.message, false);
            } else if (err.response.config.url.includes('star.playback.edge.bamgrid.com') || err.response.config.url.includes('star.content.edge.bamgrid.com')) {
              showAlert(JSON.stringify(err.response.data), false);
              console.log('star.playback.edge.bamgrid.com')
              return Promise.reject(err.response);
            } else {
              // showAlert(JSON.stringify(err.response.data), false);
              // removeValue()
              const config = err.response.config;
              if (!isRefreshing) {
                isRefreshing = true;
                AsyncStorage.getItem('user_token').then( e => {
                  console.log(e)
                  const json = e != null ? JSON.parse(e) : null;
                  refreshToken(json.refresh_token, json.access_token).then(res => {
                    let token = res.data.access_token;
                    json.access_token = token;
                    AsyncStorage.setItem('user_token', JSON.stringify(json));
                    config.headers["Authorization"] = 'Bearer ' + token;
                    requests.forEach((cb) => cb(token));
                    requests = [];
                    return instance(config);
                  }).catch(err => {
                    showAlert(JSON.stringify(err.response.data), false);
                    removeValue()
                  }).finally(() => {
                    isRefreshing = false;
                  });
                })
              } else {
                // 正在刷新token，返回一个未执行resolve的promise
                
              }
              return new Promise((resolve) => {
                // 将resolve放进队列，用一个函数形式来保存，等token刷新后直接执行
                console.log( '正在刷新token')
                requests.push((token: string) => {
                  config.headers["Authorization"] = 'Bearer ' + token;
                  resolve(instance(config));
                });
              });
       
            }
            // window.location.search = 're=' + window.location.href;
            
            // showAlert(err.response.data.msg);
            // console.log(err.response);
            // return Promise.resolve(err);
 
          } else {
            if (err.response) {
              showAlert(JSON.stringify(err.response.data), false);
            }
            return Promise.reject(err);
          }
        }
      );
    }
  }, [loaded])

  if (!loaded) {
    return null;
  }

  function showPathName() {
    console.log(pathname)
    console.log(currentPathName)
  }
  return (
    <TamaguiProvider config={config} defaultTheme="dark">
      <ToastProvider>
        <AuthProvider>
          <Stack 
            screenOptions={{ contentStyle:{backgroundColor: "#000000"}}}>
            <Stack.Screen
              name="(tabs)"
              options={{headerShown: false, contentStyle:{backgroundColor: "#000000"}} }></Stack.Screen>
            {/* <Stack.Screen
              name="other"
              options={{
                title: "",
                headerShown: true,
                headerTransparent: Platform.OS === "ios",
                headerBlurEffect: "regular",
              }}
            /> */}
            
          </Stack>
        </AuthProvider>
      </ToastProvider>
      <StatusBar style="light" />
    </TamaguiProvider>
    
  );
}
