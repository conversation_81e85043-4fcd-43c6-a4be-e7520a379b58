{"expo": {"name": "histreams-rn", "slug": "histreams-rn", "version": "2.0.2", "orientation": "default", "icon": "./assets/logo.jpg", "userInterfaceStyle": "dark", "splash": {"image": "./assets/logo.png", "resizeMode": "contain", "backgroundColor": "#000"}, "backgroundColor": "#000000", "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "net.histreams.ios"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/logo.png", "backgroundColor": "#000"}, "package": "net.histreams.android", "backgroundColor": "#000000"}, "web": {"favicon": "./assets/logo.png"}, "scheme": "histreams-scheme", "plugins": ["expo-router", ["expo-build-properties", {"android": {"usesCleartextTraffic": true}}], ["expo-build-properties", {"android": {"minSdkVersion": 24, "compileSdkVersion": 35, "targetSdkVersion": 35, "buildToolsVersion": "35.0.0", "extraMavenRepos": ["https://artifacts.bitmovin.com/artifactory/public-releases"]}, "ios": {"deploymentTarget": "15.1"}}]], "extra": {"router": {"origin": false}, "eas": {"projectId": "a64f8cf6-5d81-48bc-985b-17eff967fb21"}}, "runtimeVersion": "2.0.0"}}